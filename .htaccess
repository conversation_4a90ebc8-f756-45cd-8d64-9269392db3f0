RewriteEngine On

# Handle SEO-friendly job URLs - /emploi/slug format (exclude .php files)
RewriteRule ^emploi/([a-zA-Z0-9\-]+)/?$ job-details.php?slug=$1 [L,QSA]

# Redirect /emploi/companies.php to /companies.php
RewriteRule ^emploi/companies\.php$ /companies.php [R=301,L]

# Redirect old job-details.php URLs with ID to SEO-friendly URLs
RewriteCond %{QUERY_STRING} ^id=([0-9]+)$
RewriteRule ^job-details\.php$ job-redirect.php?id=%1 [R=301,L]

# Security: Prevent access to sensitive files
<Files "config/*">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/html "access plus 1 hour"
</IfModule>
