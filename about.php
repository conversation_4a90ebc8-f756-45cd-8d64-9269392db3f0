<?php
require_once 'config/database.php';
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>À propos - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .about-container {
            padding-top: 100px;
            min-height: 100vh;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        .hero-section h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .hero-section p {
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .content-section {
            padding: 80px 0;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }
        
        .feature-card {
            text-align: center;
            padding: 40px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-card i {
            font-size: 3rem;
            color: #2c5aa0;
            margin-bottom: 20px;
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .stats-section {
            background: #f8f9fa;
            padding: 80px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item h3 {
            font-size: 3rem;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .team-section {
            padding: 80px 0;
        }
        
        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 60px;
        }
        
        .team-member {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .team-member img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin-bottom: 20px;
            object-fit: cover;
        }
        
        .team-member-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
        
        @media (max-width: 768px) {
            .hero-section h1 {
                font-size: 2rem;
            }
            
            .features-grid,
            .stats-grid,
            .team-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="jobs.php" class="nav-link">Emplois</a></li>
                        <li><a href="companies.php" class="nav-link">Entreprises</a></li>
                        <li><a href="about.php" class="nav-link active">À propos</a></li>
                        <li><a href="contact.php" class="nav-link">Contact</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                            <?php if ($_SESSION['user_type'] === 'company'): ?>
                                <a href="company-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-building"></i> Tableau de bord
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php else: ?>
                                <a href="user-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-user"></i> Mon Profil
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="login.php" class="btn btn-outline">Connexion</a>
                            <a href="register-type.php" class="btn btn-primary">S'inscrire</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="about-container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <h1>À propos de Concours Tunisie</h1>
                <p>La plateforme d'emploi leader en Tunisie, connectant les talents avec les meilleures opportunités professionnelles</p>
            </div>
        </section>

        <!-- Mission Section -->
        <section class="content-section">
            <div class="container">
                <div style="text-align: center; margin-bottom: 60px;">
                    <h2 style="color: #2c5aa0; font-size: 2.5rem; margin-bottom: 20px;">Notre Mission</h2>
                    <p style="font-size: 1.1rem; color: #666; max-width: 800px; margin: 0 auto; line-height: 1.8;">
                        Chez Concours Tunisie, nous nous engageons à révolutionner le marché de l'emploi en Tunisie en créant 
                        un pont efficace entre les candidats talentueux et les entreprises innovantes. Notre plateforme moderne 
                        facilite la recherche d'emploi et le recrutement grâce à des outils avancés et une interface intuitive.
                    </p>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <i class="fas fa-search"></i>
                        <h3>Recherche Avancée</h3>
                        <p>Trouvez l'emploi parfait grâce à nos filtres intelligents et notre moteur de recherche performant.</p>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-handshake"></i>
                        <h3>Mise en Relation</h3>
                        <p>Connectez-vous directement avec les recruteurs et les entreprises qui correspondent à votre profil.</p>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-chart-line"></i>
                        <h3>Suivi des Candidatures</h3>
                        <p>Suivez l'évolution de vos candidatures et gérez votre parcours professionnel en temps réel.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats-section">
            <div class="container">
                <div style="text-align: center; margin-bottom: 60px;">
                    <h2 style="color: #2c5aa0; font-size: 2.5rem; margin-bottom: 20px;">Nos Chiffres</h2>
                    <p style="color: #666; font-size: 1.1rem;">Des résultats qui parlent d'eux-mêmes</p>
                </div>
                
                <div class="stats-grid">
                    <?php
                    try {
                        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        $total_jobs = $pdo->query("SELECT COUNT(*) FROM jobs WHERE is_active = 1")->fetchColumn();
                        $total_companies = $pdo->query("SELECT COUNT(*) FROM companies WHERE is_active = 1")->fetchColumn();
                        $total_users = $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn();
                        $total_applications = $pdo->query("SELECT COUNT(*) FROM job_applications")->fetchColumn();
                    } catch (Exception $e) {
                        $total_jobs = 100;
                        $total_companies = 50;
                        $total_users = 500;
                        $total_applications = 200;
                    }
                    ?>
                    
                    <div class="stat-item">
                        <h3><?php echo number_format($total_jobs); ?>+</h3>
                        <p>Offres d'emploi</p>
                    </div>
                    
                    <div class="stat-item">
                        <h3><?php echo number_format($total_companies); ?>+</h3>
                        <p>Entreprises partenaires</p>
                    </div>
                    
                    <div class="stat-item">
                        <h3><?php echo number_format($total_users); ?>+</h3>
                        <p>Candidats inscrits</p>
                    </div>
                    
                    <div class="stat-item">
                        <h3><?php echo number_format($total_applications); ?>+</h3>
                        <p>Candidatures envoyées</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Values Section -->
        <section class="content-section">
            <div class="container">
                <div style="text-align: center; margin-bottom: 60px;">
                    <h2 style="color: #2c5aa0; font-size: 2.5rem; margin-bottom: 20px;">Nos Valeurs</h2>
                </div>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <i class="fas fa-shield-alt"></i>
                        <h3>Transparence</h3>
                        <p>Nous croyons en la transparence totale dans tous nos processus et interactions.</p>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-rocket"></i>
                        <h3>Innovation</h3>
                        <p>Nous innovons constamment pour améliorer l'expérience utilisateur et les services.</p>
                    </div>
                    
                    <div class="feature-card">
                        <i class="fas fa-heart"></i>
                        <h3>Engagement</h3>
                        <p>Nous nous engageons à accompagner chaque utilisateur vers le succès professionnel.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Team Section -->
        <section class="team-section">
            <div class="container">
                <div style="text-align: center; margin-bottom: 60px;">
                    <h2 style="color: #2c5aa0; font-size: 2.5rem; margin-bottom: 20px;">Notre Équipe</h2>
                    <p style="color: #666; font-size: 1.1rem;">Des professionnels passionnés au service de votre réussite</p>
                </div>
                
                <div class="team-grid">
                    <div class="team-member">
                        <div class="team-member-placeholder">
                            <i class="fas fa-user" style="font-size: 3rem; color: #ccc;"></i>
                        </div>
                        <h3>Ahmed Ben Ali</h3>
                        <p style="color: #2c5aa0; font-weight: 600;">Directeur Général</p>
                        <p style="color: #666;">Expert en ressources humaines avec plus de 15 ans d'expérience dans le recrutement.</p>
                    </div>
                    
                    <div class="team-member">
                        <div class="team-member-placeholder">
                            <i class="fas fa-user" style="font-size: 3rem; color: #ccc;"></i>
                        </div>
                        <h3>Fatma Trabelsi</h3>
                        <p style="color: #2c5aa0; font-weight: 600;">Directrice Technique</p>
                        <p style="color: #666;">Ingénieure logiciel passionnée par l'innovation et les nouvelles technologies.</p>
                    </div>
                    
                    <div class="team-member">
                        <div class="team-member-placeholder">
                            <i class="fas fa-user" style="font-size: 3rem; color: #ccc;"></i>
                        </div>
                        <h3>Mohamed Karray</h3>
                        <p style="color: #2c5aa0; font-weight: 600;">Responsable Commercial</p>
                        <p style="color: #666;">Spécialiste en développement commercial et relations entreprises.</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.php" class="logo">
                            <i class="fas fa-briefcase"></i>
                            <span>Concours Tunisie</span>
                        </a>
                        <p>La plateforme d'emploi leader en Tunisie</p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Liens rapides</h4>
                    <ul>
                        <li><a href="jobs.php">Emplois</a></li>
                        <li><a href="companies.php">Entreprises</a></li>
                        <li><a href="about.php">À propos</a></li>
                        <li><a href="contact.php">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Pour les candidats</h4>
                    <ul>
                        <li><a href="register-type.php">Créer un compte</a></li>
                        <li><a href="profile.php">Mon profil</a></li>
                        <li><a href="user-dashboard.php">Mes candidatures</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Pour les entreprises</h4>
                    <ul>
                        <li><a href="register-type.php">Inscription entreprise</a></li>
                        <li><a href="post-job.php">Publier une offre</a></li>
                        <li><a href="company-dashboard.php">Tableau de bord</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Concours Tunisie. Tous droits réservés.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
