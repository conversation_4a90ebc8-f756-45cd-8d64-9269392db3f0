<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin-login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin = $database->fetch("SELECT * FROM admin_users WHERE id = ?", [$admin_id]);

if (!$admin) {
    session_destroy();
    header('Location: admin-login.php');
    exit;
}

$success = '';
$errors = [];

// Handle actions
if ($_POST) {
    if (isset($_POST['action'])) {
        $application_id = (int)$_POST['application_id'];
        
        switch ($_POST['action']) {
            case 'accept':
                $result = $database->update('job_applications', ['status' => 'accepted'], 'id = ?', [$application_id]);
                if ($result) {
                    $success = 'Candidature acceptée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de l\'acceptation de la candidature';
                }
                break;
                
            case 'reject':
                $result = $database->update('job_applications', ['status' => 'rejected'], 'id = ?', [$application_id]);
                if ($result) {
                    $success = 'Candidature rejetée avec succès!';
                } else {
                    $errors[] = 'Erreur lors du rejet de la candidature';
                }
                break;
                
            case 'pending':
                $result = $database->update('job_applications', ['status' => 'pending'], 'id = ?', [$application_id]);
                if ($result) {
                    $success = 'Candidature remise en attente avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la modification du statut';
                }
                break;
                
            case 'delete':
                $result = $database->delete('job_applications', 'id = ?', [$application_id]);
                if ($result) {
                    $success = 'Candidature supprimée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la suppression de la candidature';
                }
                break;
        }
    }
}

// Get search parameters
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? 'all');
$job_id = (int)($_GET['job_id'] ?? 0);
$company_id = (int)($_GET['company_id'] ?? 0);
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ? OR j.title LIKE ? OR c.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== 'all') {
    $where_conditions[] = "ja.status = ?";
    $params[] = $status;
}

if ($job_id > 0) {
    $where_conditions[] = "ja.job_id = ?";
    $params[] = $job_id;
}

if ($company_id > 0) {
    $where_conditions[] = "j.company_id = ?";
    $params[] = $company_id;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get applications
$applications = $database->fetchAll("
    SELECT ja.id, ja.status, ja.applied_at, ja.cover_letter,
           u.first_name, u.last_name, u.email, u.phone, u.governorate,
           j.title as job_title, j.id as job_id,
           c.name as company_name, c.id as company_id
    FROM job_applications ja
    JOIN users u ON ja.user_id = u.id
    JOIN jobs j ON ja.job_id = j.id
    JOIN companies c ON j.company_id = c.id
    $where_clause
    ORDER BY ja.applied_at DESC
    LIMIT $per_page OFFSET $offset
", $params);

// Get total count
$total_applications = $database->fetch("
    SELECT COUNT(*) as total 
    FROM job_applications ja
    JOIN users u ON ja.user_id = u.id
    JOIN jobs j ON ja.job_id = j.id
    JOIN companies c ON j.company_id = c.id
    $where_clause
", $params)['total'];
$total_pages = ceil($total_applications / $per_page);

// Get statistics
$stats = [
    'total' => $database->fetch("SELECT COUNT(*) as count FROM job_applications")['count'],
    'pending' => $database->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'pending'")['count'],
    'accepted' => $database->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'accepted'")['count'],
    'rejected' => $database->fetch("SELECT COUNT(*) as count FROM job_applications WHERE status = 'rejected'")['count']
];

// Get companies and jobs for filters
$companies = $database->fetchAll("SELECT id, name FROM companies ORDER BY name");
$jobs = $database->fetchAll("SELECT id, title FROM jobs ORDER BY title");
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Candidatures - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-container {
            padding: 20px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .admin-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #dc3545;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filters form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn-primary { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .applications-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-accepted { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination .current {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .cover-letter {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-paper-plane"></i> Gestion des Candidatures</h1>
                <p>Gérer toutes les candidatures soumises sur la plateforme</p>
            </div>
            <div>
                <a href="admin-dashboard.php" class="btn btn-info">
                    <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                </a>
            </div>
        </div>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <?php foreach ($errors as $error): ?>
                    <div><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><?php echo $stats['total']; ?></h3>
                <p>Total Candidatures</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['pending']; ?></h3>
                <p>En Attente</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['accepted']; ?></h3>
                <p>Acceptées</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['rejected']; ?></h3>
                <p>Rejetées</p>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="filters">
            <form method="GET">
                <div class="filter-group">
                    <label>Rechercher</label>
                    <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Candidat, email, poste, entreprise...">
                </div>
                <div class="filter-group">
                    <label>Statut</label>
                    <select name="status">
                        <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>Tous</option>
                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>En attente</option>
                        <option value="accepted" <?php echo $status === 'accepted' ? 'selected' : ''; ?>>Acceptées</option>
                        <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejetées</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Entreprise</label>
                    <select name="company_id">
                        <option value="0">Toutes les entreprises</option>
                        <?php foreach ($companies as $company): ?>
                            <option value="<?php echo $company['id']; ?>" <?php echo $company_id == $company['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($company['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Applications Table -->
        <div class="applications-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Candidat</th>
                        <th>Email</th>
                        <th>Poste</th>
                        <th>Entreprise</th>
                        <th>Gouvernorat</th>
                        <th>Statut</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($applications as $app): ?>
                    <tr>
                        <td><?php echo $app['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($app['first_name'] . ' ' . $app['last_name']); ?></strong>
                            <?php if ($app['phone']): ?>
                                <br><small><?php echo htmlspecialchars($app['phone']); ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($app['email']); ?></td>
                        <td>
                            <a href="job-details.php?id=<?php echo $app['job_id']; ?>" target="_blank">
                                <?php echo htmlspecialchars($app['job_title']); ?>
                            </a>
                        </td>
                        <td>
                            <a href="admin-companies.php?company_id=<?php echo $app['company_id']; ?>">
                                <?php echo htmlspecialchars($app['company_name']); ?>
                            </a>
                        </td>
                        <td><?php echo htmlspecialchars($app['governorate']); ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $app['status']; ?>">
                                <?php 
                                $status_text = [
                                    'pending' => 'En attente',
                                    'accepted' => 'Acceptée',
                                    'rejected' => 'Rejetée'
                                ];
                                echo $status_text[$app['status']];
                                ?>
                            </span>
                        </td>
                        <td><?php echo date('d/m/Y H:i', strtotime($app['applied_at'])); ?></td>
                        <td>
                            <div class="actions">
                                <?php if ($app['status'] === 'pending'): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="application_id" value="<?php echo $app['id']; ?>">
                                        <input type="hidden" name="action" value="accept">
                                        <button type="submit" class="btn btn-success" title="Accepter">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="application_id" value="<?php echo $app['id']; ?>">
                                        <input type="hidden" name="action" value="reject">
                                        <button type="submit" class="btn btn-warning" title="Rejeter">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="application_id" value="<?php echo $app['id']; ?>">
                                        <input type="hidden" name="action" value="pending">
                                        <button type="submit" class="btn btn-info" title="Remettre en attente">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Supprimer définitivement cette candidature?')">
                                    <input type="hidden" name="application_id" value="<?php echo $app['id']; ?>">
                                    <input type="hidden" name="action" value="delete">
                                    <button type="submit" class="btn btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                    <i class="fas fa-chevron-left"></i> Précédent
                </a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $total_pages): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                    Suivant <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
