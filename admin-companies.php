<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin-login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin = $database->fetch("SELECT * FROM admin_users WHERE id = ?", [$admin_id]);

if (!$admin) {
    session_destroy();
    header('Location: admin-login.php');
    exit;
}

$success = '';
$errors = [];

// Handle actions
if ($_POST) {
    if (isset($_POST['action'])) {
        $company_id = (int)$_POST['company_id'];
        
        switch ($_POST['action']) {
            case 'activate':
                $result = $database->update('companies', ['is_active' => 1], 'id = ?', [$company_id]);
                if ($result) {
                    $success = 'Entreprise activée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de l\'activation de l\'entreprise';
                }
                break;
                
            case 'deactivate':
                $result = $database->update('companies', ['is_active' => 0], 'id = ?', [$company_id]);
                if ($result) {
                    $success = 'Entreprise désactivée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la désactivation de l\'entreprise';
                }
                break;
                
            case 'verify':
                $result = $database->update('companies', ['is_verified' => 1, 'verification_date' => date('Y-m-d H:i:s')], 'id = ?', [$company_id]);
                if ($result) {
                    $success = 'Entreprise vérifiée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la vérification de l\'entreprise';
                }
                break;
                
            case 'delete':
                $result = $database->delete('companies', 'id = ?', [$company_id]);
                if ($result) {
                    $success = 'Entreprise supprimée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la suppression de l\'entreprise';
                }
                break;
        }
    }
}

// Get search parameters
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? 'all');
$verified = sanitize($_GET['verified'] ?? 'all');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR industry LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== 'all') {
    $where_conditions[] = "is_active = ?";
    $params[] = ($status === 'active') ? 1 : 0;
}

if ($verified !== 'all') {
    $where_conditions[] = "is_verified = ?";
    $params[] = ($verified === 'verified') ? 1 : 0;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get companies
$companies = $database->fetchAll("
    SELECT id, name, email, phone, website, industry, governorate, 
           is_active, is_verified, verification_date, created_at, last_login,
           (SELECT COUNT(*) FROM jobs WHERE company_id = companies.id) as jobs_count
    FROM companies 
    $where_clause
    ORDER BY created_at DESC
    LIMIT $per_page OFFSET $offset
", $params);

// Get total count
$total_companies = $database->fetch("SELECT COUNT(*) as total FROM companies $where_clause", $params)['total'];
$total_pages = ceil($total_companies / $per_page);

// Get statistics
$stats = [
    'total' => $database->fetch("SELECT COUNT(*) as count FROM companies")['count'],
    'active' => $database->fetch("SELECT COUNT(*) as count FROM companies WHERE is_active = 1")['count'],
    'inactive' => $database->fetch("SELECT COUNT(*) as count FROM companies WHERE is_active = 0")['count'],
    'verified' => $database->fetch("SELECT COUNT(*) as count FROM companies WHERE is_verified = 1")['count']
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Entreprises - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-container {
            padding: 20px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .admin-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #dc3545;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filters form {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .filter-group {
            flex: 1;
        }
        
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .filter-group input, .filter-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn-primary { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .jobs-table, .companies-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-verified { background: #cce5ff; color: #004085; }
        .status-unverified { background: #fff3cd; color: #856404; }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination .current {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-building"></i> Gestion des Entreprises</h1>
                <p>Gérer toutes les entreprises inscrites sur la plateforme</p>
            </div>
            <div>
                <a href="admin-dashboard.php" class="btn btn-info">
                    <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                </a>
            </div>
        </div>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <?php foreach ($errors as $error): ?>
                    <div><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><?php echo $stats['total']; ?></h3>
                <p>Total Entreprises</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['active']; ?></h3>
                <p>Entreprises Actives</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['inactive']; ?></h3>
                <p>Entreprises Inactives</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['verified']; ?></h3>
                <p>Entreprises Vérifiées</p>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="filters">
            <form method="GET">
                <div class="filter-group">
                    <label>Rechercher</label>
                    <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Nom, email, secteur...">
                </div>
                <div class="filter-group">
                    <label>Statut</label>
                    <select name="status">
                        <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>Tous</option>
                        <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Actives</option>
                        <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactives</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Vérification</label>
                    <select name="verified">
                        <option value="all" <?php echo $verified === 'all' ? 'selected' : ''; ?>>Toutes</option>
                        <option value="verified" <?php echo $verified === 'verified' ? 'selected' : ''; ?>>Vérifiées</option>
                        <option value="unverified" <?php echo $verified === 'unverified' ? 'selected' : ''; ?>>Non vérifiées</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Companies Table -->
        <div class="companies-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Secteur</th>
                        <th>Gouvernorat</th>
                        <th>Offres</th>
                        <th>Statut</th>
                        <th>Vérifiée</th>
                        <th>Inscription</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($companies as $company): ?>
                    <tr>
                        <td><?php echo $company['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($company['name']); ?></strong>
                            <?php if ($company['website']): ?>
                                <br><small><a href="<?php echo htmlspecialchars($company['website']); ?>" target="_blank"><?php echo htmlspecialchars($company['website']); ?></a></small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($company['email']); ?></td>
                        <td><?php echo htmlspecialchars($company['industry']); ?></td>
                        <td><?php echo htmlspecialchars($company['governorate']); ?></td>
                        <td><?php echo $company['jobs_count']; ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $company['is_active'] ? 'active' : 'inactive'; ?>">
                                <?php echo $company['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-<?php echo $company['is_verified'] ? 'verified' : 'unverified'; ?>">
                                <?php echo $company['is_verified'] ? 'Vérifiée' : 'Non vérifiée'; ?>
                            </span>
                        </td>
                        <td><?php echo date('d/m/Y', strtotime($company['created_at'])); ?></td>
                        <td>
                            <div class="actions">
                                <a href="admin-company-edit.php?id=<?php echo $company['id']; ?>" class="btn btn-info" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <?php if (!$company['is_verified']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                        <input type="hidden" name="action" value="verify">
                                        <button type="submit" class="btn btn-success" title="Vérifier">
                                            <i class="fas fa-check-circle"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <?php if ($company['is_active']): ?>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Désactiver cette entreprise?')">
                                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                        <input type="hidden" name="action" value="deactivate">
                                        <button type="submit" class="btn btn-warning" title="Désactiver">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                        <input type="hidden" name="action" value="activate">
                                        <button type="submit" class="btn btn-success" title="Activer">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Supprimer définitivement cette entreprise?')">
                                    <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                    <input type="hidden" name="action" value="delete">
                                    <button type="submit" class="btn btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                    <i class="fas fa-chevron-left"></i> Précédent
                </a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $total_pages): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                    Suivant <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
