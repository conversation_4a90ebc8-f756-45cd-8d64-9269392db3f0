<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin-login.php');
    exit;
}

$company_id = (int)($_GET['id'] ?? 0);
if ($company_id <= 0) {
    header('Location: admin-companies.php');
    exit;
}

$success = '';
$errors = [];

// Get company details
$company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$company_id]);
if (!$company) {
    header('Location: admin-companies.php?error=company_not_found');
    exit;
}

// Handle form submission
if ($_POST) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $website = sanitize($_POST['website'] ?? '');
    $industry = sanitize($_POST['industry'] ?? '');
    $employee_count = sanitize($_POST['employee_count'] ?? '');
    $governorate = sanitize($_POST['governorate'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $is_active = (int)($_POST['is_active'] ?? 0);
    $is_verified = (int)($_POST['is_verified'] ?? 0);
    
    // Validation
    if (empty($name)) $errors[] = 'Le nom de l\'entreprise est requis';
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    
    // Check if email exists for other companies
    $existing = $database->fetch("SELECT id FROM companies WHERE email = ? AND id != ?", [$email, $company_id]);
    if ($existing) {
        $errors[] = 'Cet email est déjà utilisé par une autre entreprise';
    }
    
    if (empty($errors)) {
        $update_data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'website' => $website,
            'industry' => $industry,
            'employee_count' => $employee_count,
            'governorate' => $governorate,
            'description' => $description,
            'is_active' => $is_active,
            'is_verified' => $is_verified
        ];
        
        $result = $database->update('companies', $update_data, 'id = ?', [$company_id]);
        
        if ($result) {
            $success = 'Entreprise mise à jour avec succès!';
            // Refresh company data
            $company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$company_id]);
        } else {
            $errors[] = 'Erreur lors de la mise à jour de l\'entreprise';
        }
    }
}

$governorates = [
    'tunis' => 'Tunis',
    'ariana' => 'Ariana',
    'ben_arous' => 'Ben Arous',
    'manouba' => 'Manouba',
    'nabeul' => 'Nabeul',
    'zaghouan' => 'Zaghouan',
    'bizerte' => 'Bizerte',
    'beja' => 'Béja',
    'jendouba' => 'Jendouba',
    'kef' => 'Le Kef',
    'siliana' => 'Siliana',
    'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine',
    'sidi_bouzid' => 'Sidi Bouzid',
    'sousse' => 'Sousse',
    'monastir' => 'Monastir',
    'mahdia' => 'Mahdia',
    'sfax' => 'Sfax',
    'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur',
    'kebili' => 'Kébili',
    'gabes' => 'Gabès',
    'medenine' => 'Médenine',
    'tataouine' => 'Tataouine'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier Entreprise - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-container {
            padding: 20px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .admin-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .edit-form {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #dc3545;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .checkbox-group {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 1rem;
            font-weight: 600;
            transition: background 0.3s;
        }
        
        .btn-primary { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-primary:hover { background: #c82333; }
        .btn-secondary:hover { background: #5a6268; }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .company-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .company-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .company-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .stat-item strong {
            display: block;
            font-size: 1.2rem;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-edit"></i> Modifier Entreprise</h1>
                <p>Modifier les informations de l'entreprise</p>
            </div>
            <div>
                <a href="admin-companies.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
            </div>
        </div>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <?php foreach ($errors as $error): ?>
                    <div><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Company Info -->
        <div class="company-info">
            <h3><?php echo htmlspecialchars($company['name']); ?></h3>
            <p><strong>ID:</strong> <?php echo $company['id']; ?> | <strong>Créée le:</strong> <?php echo date('d/m/Y', strtotime($company['created_at'])); ?></p>
            
            <div class="company-stats">
                <div class="stat-item">
                    <strong><?php echo $database->fetch("SELECT COUNT(*) as count FROM jobs WHERE company_id = ?", [$company_id])['count']; ?></strong>
                    <span>Offres publiées</span>
                </div>
                <div class="stat-item">
                    <strong><?php echo $database->fetch("SELECT COUNT(*) as count FROM job_applications ja JOIN jobs j ON ja.job_id = j.id WHERE j.company_id = ?", [$company_id])['count']; ?></strong>
                    <span>Candidatures reçues</span>
                </div>
                <div class="stat-item">
                    <strong><?php echo $company['last_login'] ? date('d/m/Y', strtotime($company['last_login'])) : 'Jamais'; ?></strong>
                    <span>Dernière connexion</span>
                </div>
            </div>
        </div>
        
        <!-- Edit Form -->
        <div class="edit-form">
            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Nom de l'entreprise *</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($company['name'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($company['email'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Téléphone</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($company['phone'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="website">Site web</label>
                        <input type="url" id="website" name="website" value="<?php echo htmlspecialchars($company['website'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="industry">Secteur d'activité</label>
                        <input type="text" id="industry" name="industry" value="<?php echo htmlspecialchars($company['industry'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="employee_count">Taille de l'entreprise</label>
                        <select id="employee_count" name="employee_count">
                            <option value="">Choisir la taille</option>
                            <option value="1-10" <?php echo $company['employee_count'] === '1-10' ? 'selected' : ''; ?>>1-10 employés</option>
                            <option value="11-50" <?php echo $company['employee_count'] === '11-50' ? 'selected' : ''; ?>>11-50 employés</option>
                            <option value="51-200" <?php echo $company['employee_count'] === '51-200' ? 'selected' : ''; ?>>51-200 employés</option>
                            <option value="201-500" <?php echo $company['employee_count'] === '201-500' ? 'selected' : ''; ?>>201-500 employés</option>
                            <option value="501-1000" <?php echo $company['employee_count'] === '501-1000' ? 'selected' : ''; ?>>501-1000 employés</option>
                            <option value="1000+" <?php echo $company['employee_count'] === '1000+' ? 'selected' : ''; ?>>1000+ employés</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="governorate">Gouvernorat</label>
                        <select id="governorate" name="governorate">
                            <option value="">Choisir le gouvernorat</option>
                            <?php foreach ($governorates as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo $company['governorate'] === $value ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">Description de l'entreprise</label>
                    <textarea id="description" name="description" rows="4"><?php echo htmlspecialchars($company['description'] ?? ''); ?></textarea>
                </div>
                
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_active" name="is_active" value="1" <?php echo $company['is_active'] ? 'checked' : ''; ?>>
                        <label for="is_active">Entreprise active</label>
                    </div>
                    
                    <div class="checkbox-item">
                        <input type="checkbox" id="is_verified" name="is_verified" value="1" <?php echo $company['is_verified'] ? 'checked' : ''; ?>>
                        <label for="is_verified">Entreprise vérifiée</label>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                    <a href="admin-companies.php" class="btn btn-secondary" style="margin-left: 15px;">
                        <i class="fas fa-times"></i> Annuler
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
