<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin-login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin = $database->fetch("SELECT * FROM admin_users WHERE id = ?", [$admin_id]);

if (!$admin) {
    session_destroy();
    header('Location: admin-login.php');
    exit;
}

$success = '';
$errors = [];

// Handle account approval/rejection
if ($_POST) {
    if (isset($_POST['approve_user'])) {
        $user_id = (int)$_POST['user_id'];
        $result = $database->query("UPDATE users SET is_active = 1 WHERE id = ?", [$user_id]);
        if ($result) {
            $success = 'Compte utilisateur approuvé avec succès!';
        } else {
            $errors[] = 'Erreur lors de l\'approbation du compte utilisateur';
        }
    }
    
    if (isset($_POST['reject_user'])) {
        $user_id = (int)$_POST['user_id'];
        $result = $database->query("DELETE FROM users WHERE id = ?", [$user_id]);
        if ($result) {
            $success = 'Compte utilisateur rejeté et supprimé!';
        } else {
            $errors[] = 'Erreur lors du rejet du compte utilisateur';
        }
    }
    
    if (isset($_POST['approve_company'])) {
        $company_id = (int)$_POST['company_id'];
        $result = $database->query("UPDATE companies SET is_active = 1 WHERE id = ?", [$company_id]);
        if ($result) {
            $success = 'Compte entreprise approuvé avec succès!';
        } else {
            $errors[] = 'Erreur lors de l\'approbation du compte entreprise';
        }
    }
    
    if (isset($_POST['reject_company'])) {
        $company_id = (int)$_POST['company_id'];
        $result = $database->query("DELETE FROM companies WHERE id = ?", [$company_id]);
        if ($result) {
            $success = 'Compte entreprise rejeté et supprimé!';
        } else {
            $errors[] = 'Erreur lors du rejet du compte entreprise';
        }
    }
}

// Get pending users
$pending_users = $database->fetchAll("
    SELECT id, first_name, last_name, email, phone, governorate, created_at
    FROM users 
    WHERE is_active = 0 
    ORDER BY created_at DESC
");

// Get pending companies
$pending_companies = $database->fetchAll("
    SELECT id, name, email, phone, website, industry, governorate, created_at
    FROM companies 
    WHERE is_active = 0 
    ORDER BY created_at DESC
");

// Get statistics
$stats = [
    'total_users' => $database->fetch("SELECT COUNT(*) as count FROM users")['count'],
    'active_users' => $database->fetch("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'],
    'pending_users' => count($pending_users),
    'total_companies' => $database->fetch("SELECT COUNT(*) as count FROM companies")['count'],
    'active_companies' => $database->fetch("SELECT COUNT(*) as count FROM companies WHERE is_active = 1")['count'],
    'pending_companies' => count($pending_companies),
    'total_jobs' => $database->fetch("SELECT COUNT(*) as count FROM jobs")['count'],
    'total_applications' => $database->fetch("SELECT COUNT(*) as count FROM job_applications")['count']
];

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis', 'ariana' => 'Ariana', 'ben_arous' => 'Ben Arous', 'manouba' => 'Manouba',
    'nabeul' => 'Nabeul', 'zaghouan' => 'Zaghouan', 'bizerte' => 'Bizerte', 'beja' => 'Béja',
    'jendouba' => 'Jendouba', 'kef' => 'Le Kef', 'siliana' => 'Siliana', 'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine', 'sidi_bouzid' => 'Sidi Bouzid', 'sousse' => 'Sousse',
    'monastir' => 'Monastir', 'mahdia' => 'Mahdia', 'sfax' => 'Sfax', 'gabes' => 'Gabès',
    'medenine' => 'Médenine', 'tataouine' => 'Tataouine', 'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur', 'kebili' => 'Kébili'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Admin - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-container {
            padding-top: 80px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .admin-header {
            background: #dc3545;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav h1 {
            margin: 0;
            color: white;
        }
        
        .admin-actions a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .stat-card.users i { color: #007bff; }
        .stat-card.companies i { color: #28a745; }
        .stat-card.jobs i { color: #ffc107; }
        .stat-card.applications i { color: #17a2b8; }
        
        .stat-card h3 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-card p {
            color: #666;
            margin: 0;
        }
        
        .management-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .management-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .management-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            position: relative;
        }

        .management-card:hover {
            border-color: #dc3545;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #333;
        }

        .management-card i {
            font-size: 2.5rem;
            color: #dc3545;
            margin-bottom: 15px;
        }

        .management-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.2rem;
        }

        .management-card p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 0.9rem;
        }

        .card-count {
            background: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .pending-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            margin: 0;
            color: #333;
        }
        
        .section-content {
            padding: 25px;
        }
        
        .pending-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .pending-item:last-child {
            border-bottom: none;
        }
        
        .pending-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .pending-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .pending-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-approve {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-reject {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn-approve:hover {
            background: #218838;
        }
        
        .btn-reject:hover {
            background: #c82333;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div class="container">
                <div class="admin-nav">
                    <h1><i class="fas fa-shield-alt"></i> Administration</h1>
                    <div class="admin-actions">
                        <span>Bonjour, <?php echo htmlspecialchars($admin['full_name']); ?></span>
                        <a href="index.php"><i class="fas fa-home"></i> Site</a>
                        <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Déconnexion</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card users">
                    <i class="fas fa-users"></i>
                    <h3><?php echo $stats['total_users']; ?></h3>
                    <p>Utilisateurs total</p>
                    <small><?php echo $stats['active_users']; ?> actifs, <?php echo $stats['pending_users']; ?> en attente</small>
                </div>
                
                <div class="stat-card companies">
                    <i class="fas fa-building"></i>
                    <h3><?php echo $stats['total_companies']; ?></h3>
                    <p>Entreprises total</p>
                    <small><?php echo $stats['active_companies']; ?> actives, <?php echo $stats['pending_companies']; ?> en attente</small>
                </div>
                
                <div class="stat-card jobs">
                    <i class="fas fa-briefcase"></i>
                    <h3><?php echo $stats['total_jobs']; ?></h3>
                    <p>Offres d'emploi</p>
                </div>
                
                <div class="stat-card applications">
                    <i class="fas fa-paper-plane"></i>
                    <h3><?php echo $stats['total_applications']; ?></h3>
                    <p>Candidatures</p>
                </div>
            </div>

            <!-- Management Links -->
            <div class="management-section">
                <div class="section-header">
                    <h2><i class="fas fa-cogs"></i> Gestion de la plateforme</h2>
                </div>
                <div class="management-grid">
                    <a href="admin-users.php" class="management-card">
                        <i class="fas fa-users"></i>
                        <h3>Gestion des Utilisateurs</h3>
                        <p>Voir, modifier et gérer tous les candidats</p>
                        <span class="card-count"><?php echo $stats['total_users']; ?> utilisateurs</span>
                    </a>

                    <a href="admin-companies.php" class="management-card">
                        <i class="fas fa-building"></i>
                        <h3>Gestion des Entreprises</h3>
                        <p>Voir, modifier et gérer toutes les entreprises</p>
                        <span class="card-count"><?php echo $stats['total_companies']; ?> entreprises</span>
                    </a>

                    <a href="admin-jobs.php" class="management-card">
                        <i class="fas fa-briefcase"></i>
                        <h3>Gestion des Offres</h3>
                        <p>Voir, modifier et gérer toutes les offres d'emploi</p>
                        <span class="card-count"><?php echo $stats['total_jobs']; ?> offres</span>
                    </a>

                    <a href="admin-applications.php" class="management-card">
                        <i class="fas fa-paper-plane"></i>
                        <h3>Gestion des Candidatures</h3>
                        <p>Voir et gérer toutes les candidatures</p>
                        <span class="card-count"><?php echo $stats['total_applications']; ?> candidatures</span>
                    </a>
                </div>
            </div>

            <!-- Pending Users -->
            <div class="pending-section">
                <div class="section-header">
                    <h2><i class="fas fa-user-clock"></i> Comptes utilisateurs en attente (<?php echo count($pending_users); ?>)</h2>
                </div>
                <div class="section-content">
                    <?php if (empty($pending_users)): ?>
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p>Aucun compte utilisateur en attente d'approbation</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pending_users as $user): ?>
                            <div class="pending-item">
                                <div class="pending-info">
                                    <h4><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h4>
                                    <p><?php echo htmlspecialchars($user['email']); ?> • <?php echo htmlspecialchars($user['phone']); ?></p>
                                    <p><?php echo $governorates[$user['governorate']] ?? $user['governorate']; ?> • Inscrit le <?php echo date('d/m/Y', strtotime($user['created_at'])); ?></p>
                                </div>
                                <div class="pending-actions">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="approve_user" class="btn-approve" onclick="return confirm('Approuver ce compte utilisateur?')">
                                            <i class="fas fa-check"></i> Approuver
                                        </button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="reject_user" class="btn-reject" onclick="return confirm('Rejeter et supprimer ce compte?')">
                                            <i class="fas fa-times"></i> Rejeter
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Pending Companies -->
            <div class="pending-section">
                <div class="section-header">
                    <h2><i class="fas fa-building-user"></i> Comptes entreprises en attente (<?php echo count($pending_companies); ?>)</h2>
                </div>
                <div class="section-content">
                    <?php if (empty($pending_companies)): ?>
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p>Aucun compte entreprise en attente d'approbation</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($pending_companies as $company): ?>
                            <div class="pending-item">
                                <div class="pending-info">
                                    <h4><?php echo htmlspecialchars($company['name']); ?></h4>
                                    <p><?php echo htmlspecialchars($company['email']); ?> • <?php echo htmlspecialchars($company['phone']); ?></p>
                                    <p>
                                        <?php if ($company['website']): ?>
                                            <a href="<?php echo htmlspecialchars($company['website']); ?>" target="_blank"><?php echo htmlspecialchars($company['website']); ?></a> • 
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($company['industry']); ?> • 
                                        <?php echo $governorates[$company['governorate']] ?? $company['governorate']; ?>
                                    </p>
                                    <p>Inscrite le <?php echo date('d/m/Y', strtotime($company['created_at'])); ?></p>
                                </div>
                                <div class="pending-actions">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                        <button type="submit" name="approve_company" class="btn-approve" onclick="return confirm('Approuver ce compte entreprise?')">
                                            <i class="fas fa-check"></i> Approuver
                                        </button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="company_id" value="<?php echo $company['id']; ?>">
                                        <button type="submit" name="reject_company" class="btn-reject" onclick="return confirm('Rejeter et supprimer ce compte?')">
                                            <i class="fas fa-times"></i> Rejeter
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
