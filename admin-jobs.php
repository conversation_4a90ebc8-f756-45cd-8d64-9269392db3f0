<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin-login.php');
    exit;
}

$admin_id = $_SESSION['admin_id'];
$admin = $database->fetch("SELECT * FROM admin_users WHERE id = ?", [$admin_id]);

if (!$admin) {
    session_destroy();
    header('Location: admin-login.php');
    exit;
}

$success = '';
$errors = [];

// Handle actions
if ($_POST) {
    if (isset($_POST['action'])) {
        $job_id = (int)$_POST['job_id'];
        
        switch ($_POST['action']) {
            case 'activate':
                $result = $database->update('jobs', ['is_active' => 1], 'id = ?', [$job_id]);
                if ($result) {
                    $success = 'Offre activée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de l\'activation de l\'offre';
                }
                break;
                
            case 'deactivate':
                $result = $database->update('jobs', ['is_active' => 0], 'id = ?', [$job_id]);
                if ($result) {
                    $success = 'Offre désactivée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la désactivation de l\'offre';
                }
                break;
                
            case 'delete':
                $result = $database->delete('jobs', 'id = ?', [$job_id]);
                if ($result) {
                    $success = 'Offre supprimée avec succès!';
                } else {
                    $errors[] = 'Erreur lors de la suppression de l\'offre';
                }
                break;
        }
    }
}

// Get search parameters
$search = sanitize($_GET['search'] ?? '');
$status = sanitize($_GET['status'] ?? 'all');
$job_type = sanitize($_GET['job_type'] ?? 'all');
$company_id = (int)($_GET['company_id'] ?? 0);
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build query
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(j.title LIKE ? OR j.description LIKE ? OR c.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status !== 'all') {
    $where_conditions[] = "j.is_active = ?";
    $params[] = ($status === 'active') ? 1 : 0;
}

if ($job_type !== 'all') {
    $where_conditions[] = "j.job_type = ?";
    $params[] = $job_type;
}

if ($company_id > 0) {
    $where_conditions[] = "j.company_id = ?";
    $params[] = $company_id;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get jobs
$jobs = $database->fetchAll("
    SELECT j.id, j.title, j.job_type, j.location, j.salary_min, j.salary_max, 
           j.is_active, j.created_at, j.views_count,
           c.name as company_name, c.id as company_id,
           (SELECT COUNT(*) FROM job_applications WHERE job_id = j.id) as applications_count
    FROM jobs j
    JOIN companies c ON j.company_id = c.id
    $where_clause
    ORDER BY j.created_at DESC
    LIMIT $per_page OFFSET $offset
", $params);

// Get total count
$total_jobs = $database->fetch("
    SELECT COUNT(*) as total 
    FROM jobs j 
    JOIN companies c ON j.company_id = c.id 
    $where_clause
", $params)['total'];
$total_pages = ceil($total_jobs / $per_page);

// Get statistics
$stats = [
    'total' => $database->fetch("SELECT COUNT(*) as count FROM jobs")['count'],
    'active' => $database->fetch("SELECT COUNT(*) as count FROM jobs WHERE is_active = 1")['count'],
    'inactive' => $database->fetch("SELECT COUNT(*) as count FROM jobs WHERE is_active = 0")['count'],
    'applications' => $database->fetch("SELECT COUNT(*) as count FROM job_applications")['count']
];

// Get companies for filter
$companies = $database->fetchAll("SELECT id, name FROM companies ORDER BY name");
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Offres d'Emploi - Admin</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-container {
            padding: 20px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .admin-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #dc3545;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .filters form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .btn-primary { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        
        .applications-table, .jobs-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        
        .job-type-badge {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .type-cdi { background: #e3f2fd; color: #1976d2; }
        .type-cdd { background: #fff3e0; color: #f57c00; }
        .type-stage { background: #f3e5f5; color: #7b1fa2; }
        .type-freelance { background: #e8f5e8; color: #388e3c; }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination .current {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .salary-range {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-briefcase"></i> Gestion des Offres d'Emploi</h1>
                <p>Gérer toutes les offres d'emploi publiées sur la plateforme</p>
            </div>
            <div>
                <a href="admin-dashboard.php" class="btn btn-info">
                    <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                </a>
            </div>
        </div>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <?php foreach ($errors as $error): ?>
                    <div><?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><?php echo $stats['total']; ?></h3>
                <p>Total Offres</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['active']; ?></h3>
                <p>Offres Actives</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['inactive']; ?></h3>
                <p>Offres Inactives</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['applications']; ?></h3>
                <p>Total Candidatures</p>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="filters">
            <form method="GET">
                <div class="filter-group">
                    <label>Rechercher</label>
                    <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Titre, description, entreprise...">
                </div>
                <div class="filter-group">
                    <label>Statut</label>
                    <select name="status">
                        <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>Toutes</option>
                        <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Actives</option>
                        <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactives</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Type de contrat</label>
                    <select name="job_type">
                        <option value="all" <?php echo $job_type === 'all' ? 'selected' : ''; ?>>Tous</option>
                        <option value="cdi" <?php echo $job_type === 'cdi' ? 'selected' : ''; ?>>CDI</option>
                        <option value="cdd" <?php echo $job_type === 'cdd' ? 'selected' : ''; ?>>CDD</option>
                        <option value="stage" <?php echo $job_type === 'stage' ? 'selected' : ''; ?>>Stage</option>
                        <option value="freelance" <?php echo $job_type === 'freelance' ? 'selected' : ''; ?>>Freelance</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Entreprise</label>
                    <select name="company_id">
                        <option value="0">Toutes les entreprises</option>
                        <?php foreach ($companies as $company): ?>
                            <option value="<?php echo $company['id']; ?>" <?php echo $company_id == $company['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($company['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Rechercher
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Jobs Table -->
        <div class="jobs-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Titre</th>
                        <th>Entreprise</th>
                        <th>Type</th>
                        <th>Lieu</th>
                        <th>Salaire</th>
                        <th>Candidatures</th>
                        <th>Vues</th>
                        <th>Statut</th>
                        <th>Publié</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($jobs as $job): ?>
                    <tr>
                        <td><?php echo $job['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($job['title']); ?></strong>
                        </td>
                        <td>
                            <a href="admin-companies.php?company_id=<?php echo $job['company_id']; ?>">
                                <?php echo htmlspecialchars($job['company_name']); ?>
                            </a>
                        </td>
                        <td>
                            <span class="job-type-badge type-<?php echo $job['job_type']; ?>">
                                <?php echo strtoupper($job['job_type']); ?>
                            </span>
                        </td>
                        <td><?php echo htmlspecialchars($job['location']); ?></td>
                        <td>
                            <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                <div class="salary-range">
                                    <?php echo number_format($job['salary_min']); ?> - <?php echo number_format($job['salary_max']); ?> TND
                                </div>
                            <?php else: ?>
                                <span class="text-muted">Non spécifié</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $job['applications_count']; ?></td>
                        <td><?php echo $job['views_count']; ?></td>
                        <td>
                            <span class="status-badge status-<?php echo $job['is_active'] ? 'active' : 'inactive'; ?>">
                                <?php echo $job['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td><?php echo date('d/m/Y', strtotime($job['created_at'])); ?></td>
                        <td>
                            <div class="actions">
                                <a href="job-details.php?id=<?php echo $job['id']; ?>" class="btn btn-info" title="Voir" target="_blank">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                <?php if ($job['is_active']): ?>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Désactiver cette offre?')">
                                        <input type="hidden" name="job_id" value="<?php echo $job['id']; ?>">
                                        <input type="hidden" name="action" value="deactivate">
                                        <button type="submit" class="btn btn-warning" title="Désactiver">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="job_id" value="<?php echo $job['id']; ?>">
                                        <input type="hidden" name="action" value="activate">
                                        <button type="submit" class="btn btn-success" title="Activer">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Supprimer définitivement cette offre?')">
                                    <input type="hidden" name="job_id" value="<?php echo $job['id']; ?>">
                                    <input type="hidden" name="action" value="delete">
                                    <button type="submit" class="btn btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                    <i class="fas fa-chevron-left"></i> Précédent
                </a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $total_pages): ?>
                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                    Suivant <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
