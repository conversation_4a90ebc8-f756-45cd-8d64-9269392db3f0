<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = '';

// Check for logout message
if (isset($_GET['message']) && $_GET['message'] === 'logged_out') {
    $success = 'Vous avez été déconnecté avec succès.';
}

if ($_POST) {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';

    // Debug logging (remove in production)
    error_log("Admin login attempt - Username: $username");

    // Validation
    if (empty($username)) $errors[] = 'Le nom d\'utilisateur est requis';
    if (empty($password)) $errors[] = 'Le mot de passe est requis';

    if (empty($errors)) {
        $admin = $database->fetch("SELECT * FROM admin_users WHERE username = ? AND is_active = 1", [$username]);

        if ($admin) {
            error_log("Admin found: " . $admin['username'] . " (ID: " . $admin['id'] . ")");

            if (verifyPassword($password, $admin['password'])) {
                // Start admin session
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_name'] = $admin['full_name'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_logged_in'] = true;

                error_log("Admin login successful: " . $admin['username']);

                // Update last login
                $database->query("UPDATE admin_users SET last_login = NOW() WHERE id = ?", [$admin['id']]);

                header('Location: admin-dashboard.php');
                exit;
            } else {
                error_log("Password verification failed for: $username");
                $errors[] = 'Nom d\'utilisateur ou mot de passe incorrect';
            }
        } else {
            error_log("Admin user not found or inactive: $username");
            $errors[] = 'Nom d\'utilisateur ou mot de passe incorrect';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion Admin - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .admin-login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #dc3545 0%, #a71e2a 100%);
            padding: 20px;
        }
        
        .admin-login-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 450px;
            width: 100%;
        }
        
        .admin-login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .admin-login-header h1 {
            color: #dc3545;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .admin-badge {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #dc3545;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .btn-admin {
            background: #dc3545;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        
        .btn-admin:hover {
            background: #a71e2a;
        }
    </style>
</head>
<body>
    <div class="admin-login-container">
        <div class="admin-login-form">
            <div class="admin-login-header">
                <h1><i class="fas fa-shield-alt"></i> Admin Panel</h1>
                <div class="admin-badge">
                    <i class="fas fa-lock"></i>
                    Accès Administrateur
                </div>
                <p>Connectez-vous pour gérer la plateforme</p>
            </div>
            
            <?php if (!empty($success)): ?>
                <div class="alert alert-success" style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb;">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="username">Nom d'utilisateur</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn-admin">
                    <i class="fas fa-sign-in-alt"></i>
                    Se connecter
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                <p><a href="index.php" style="color: #666;">← Retour au site</a></p>
                <small style="color: #999;">Accès réservé aux administrateurs</small>
            </div>
        </div>
    </div>
</body>
</html>
