<?php
session_start();

// Destroy admin session
if (isset($_SESSION['admin_logged_in'])) {
    // Clear all session variables
    $_SESSION = array();
    
    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Destroy the session
    session_destroy();
}

// Redirect to admin login
header('Location: admin-login.php?message=logged_out');
exit;
?>
