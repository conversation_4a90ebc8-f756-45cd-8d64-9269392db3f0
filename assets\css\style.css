/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    direction: ltr;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #2c5aa0;
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-brand .logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    color: #2c5aa0;
}

.nav-actions {
    display: flex;
    gap: 1rem;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background: #2c5aa0;
    color: white;
}

.btn-primary:hover {
    background: #1e3f73;
}

.btn-outline {
    background: transparent;
    color: #2c5aa0;
    border: 2px solid #2c5aa0;
}

.btn-outline:hover {
    background: #2c5aa0;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-search {
    background: #28a745;
    color: white;
    padding: 1rem 2rem;
}

.btn-search:hover {
    background: #1e7e34;
}

.btn-apply {
    background: #17a2b8;
    color: white;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.btn-apply:hover {
    background: #117a8b;
}

/* Login Dropdown Styles */
.login-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    cursor: pointer;
    position: relative;
}

.dropdown-toggle::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    margin-left: 8px;
    font-size: 0.8rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.login-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s;
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
    color: #2c5aa0;
}

/* Header Styles */
.main-header {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
}

.navbar-brand a {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: #2c5aa0;
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-menu ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.navbar-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.navbar-menu a:hover,
.navbar-menu a.active {
    color: #2c5aa0;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.mobile-menu-toggle {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
    color: #333;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    z-index: 999;
}

.mobile-menu.active {
    display: block;
}

.mobile-menu-content {
    padding: 20px;
}

.mobile-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-menu li {
    margin-bottom: 15px;
}

.mobile-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
}

.mobile-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Footer Styles */
.main-footer {
    background: #2c3e50;
    color: white;
    margin-top: 50px;
}

.footer-content {
    padding: 50px 0 30px;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
    color: white;
}

.footer-section ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #34495e;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: background 0.3s;
}

.social-links a:hover {
    background: #2c5aa0;
}

.contact-info p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.newsletter-form {
    display: flex;
    margin-top: 10px;
}

.newsletter-form input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px 0 0 5px;
}

.newsletter-form button {
    padding: 10px 15px;
    background: #2c5aa0;
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.footer-bottom {
    background: #1a252f;
    padding: 20px 0;
    border-top: 1px solid #34495e;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    font-size: 0.9rem;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #2c5aa0;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: #1e3d72;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-menu,
    .navbar-actions {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

/* Job Search Form */
.job-search-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 3rem;
}

.search-form {
    max-width: 800px;
    margin: 0 auto;
}

.search-group {
    display: grid;
    grid-template-columns: 2fr 1.2fr 1.2fr 1.2fr auto;
    gap: 1rem;
    align-items: end;
}

.search-field {
    position: relative;
}

.search-field i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-field input,
.search-field select {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 1rem;
    color: #333;
}

.search-field input:focus,
.search-field select:focus {
    outline: none;
    border-color: #2c5aa0;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #ffc107;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
}

.section-footer {
    text-align: center;
    margin-top: 3rem;
}

/* Featured Jobs Section */
.featured-jobs {
    padding: 80px 0;
    background: white;
}

.jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.job-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.job-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.company-logo {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.company-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.company-logo::before {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c5aa0, #1e3f73);
    border-radius: 10px;
    position: absolute;
    top: 0;
    left: 0;
}

.company-logo::after {
    content: '\f1ad';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 1.5rem;
    z-index: 1;
}

.company-logo {
    position: relative;
}

.company-logo img {
    position: relative;
    z-index: 2;
}

.job-info {
    flex: 1;
}

.job-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.company-name {
    color: #666;
    margin-bottom: 0.5rem;
}

.job-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #888;
}

.job-meta i {
    margin-left: 5px;
}

.job-description {
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.6;
}

.job-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.job-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.salary {
    font-weight: 600;
    color: #28a745;
    font-size: 1.1rem;
}

/* How it Works Section */
.how-it-works {
    padding: 80px 0;
    background: #f8f9fa;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step-item {
    text-align: center;
    padding: 2rem;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: #2c5aa0;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
}

.step-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.step-description {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-brand .logo {
    color: white;
    margin-bottom: 1rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #ffc107;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: #ccc;
    font-size: 1.2rem;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .search-group {
        grid-template-columns: 1fr;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .jobs-grid {
        grid-template-columns: 1fr;
    }
    
    .steps-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .filter-row {
        grid-template-columns: 1fr !important;
    }
}
