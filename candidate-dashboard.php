<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if candidate is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'candidate') {
    header('Location: login-candidate.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user = $database->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    header('Location: login-candidate.php');
    exit;
}

// Get user statistics
$stats = [
    'applications' => $database->fetch("SELECT COUNT(*) as count FROM job_applications WHERE user_id = ?", [$user_id])['count'],
    'profile_views' => $user['profile_views'] ?? 0,
    'saved_jobs' => 0 // We'll implement this later
];

// Get recent applications
$recent_applications = $database->fetchAll("
    SELECT ja.*, j.title as job_title, c.name as company_name, ja.applied_at
    FROM job_applications ja
    JOIN jobs j ON ja.job_id = j.id
    JOIN companies c ON j.company_id = c.id
    WHERE ja.user_id = ?
    ORDER BY ja.applied_at DESC
    LIMIT 5
", [$user_id]);

// Get recommended jobs (simple algorithm based on user's skills)
$recommended_jobs = $database->fetchAll("
    SELECT j.*, c.name as company_name, c.logo as company_logo
    FROM jobs j
    JOIN companies c ON j.company_id = c.id
    WHERE j.is_active = 1 AND c.is_active = 1
    ORDER BY j.created_at DESC
    LIMIT 6
");
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - <?php echo htmlspecialchars($_SESSION['user_name']); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .dashboard-container {
            padding-top: 80px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
        }
        
        .welcome-section {
            text-align: center;
        }
        
        .welcome-section h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .stat-card.applications i { color: #667eea; }
        .stat-card.views i { color: #f093fb; }
        .stat-card.saved i { color: #4facfe; }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #333;
        }
        
        .dashboard-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .section-header h2 {
            margin: 0;
            color: #333;
            font-size: 1.5rem;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .application-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .application-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .application-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-accepted { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .job-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .job-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .job-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .job-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .job-card .company {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .job-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .btn-apply {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s;
        }
        
        .btn-apply:hover {
            background: #5a6fd8;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .quick-action {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            text-decoration: none;
            color: #333;
            transition: transform 0.3s;
            flex: 1;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: #333;
        }
        
        .quick-action i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="container">
                <div class="welcome-section">
                    <h1>Bienvenue, <?php echo htmlspecialchars($_SESSION['first_name'] ?? $_SESSION['user_name']); ?>!</h1>
                    <p>Gérez votre carrière et trouvez votre emploi idéal</p>
                </div>
            </div>
        </div>
        
        <div class="container">
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card applications">
                    <i class="fas fa-paper-plane"></i>
                    <h3><?php echo $stats['applications']; ?></h3>
                    <p>Candidatures envoyées</p>
                </div>
                
                <div class="stat-card views">
                    <i class="fas fa-eye"></i>
                    <h3><?php echo $stats['profile_views']; ?></h3>
                    <p>Vues du profil</p>
                </div>
                
                <div class="stat-card saved">
                    <i class="fas fa-bookmark"></i>
                    <h3><?php echo $stats['saved_jobs']; ?></h3>
                    <p>Emplois sauvegardés</p>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="profile.php" class="quick-action">
                    <i class="fas fa-user-edit"></i>
                    <h4>Modifier le profil</h4>
                    <p>Mettez à jour vos informations</p>
                </a>
                
                <a href="jobs.php" class="quick-action">
                    <i class="fas fa-search"></i>
                    <h4>Rechercher des emplois</h4>
                    <p>Trouvez votre emploi idéal</p>
                </a>
                
                <a href="my-applications.php" class="quick-action">
                    <i class="fas fa-list"></i>
                    <h4>Mes candidatures</h4>
                    <p>Suivez vos candidatures</p>
                </a>
                
                <a href="logout.php" class="quick-action">
                    <i class="fas fa-sign-out-alt"></i>
                    <h4>Se déconnecter</h4>
                    <p>Quitter votre session</p>
                </a>
            </div>
            
            <!-- Recent Applications -->
            <?php if (!empty($recent_applications)): ?>
            <div class="dashboard-section">
                <div class="section-header">
                    <h2><i class="fas fa-history"></i> Candidatures récentes</h2>
                </div>
                <div class="section-content">
                    <?php foreach ($recent_applications as $app): ?>
                    <div class="application-item">
                        <div class="application-info">
                            <h4><?php echo htmlspecialchars($app['job_title']); ?></h4>
                            <p><?php echo htmlspecialchars($app['company_name']); ?> • Candidature envoyée le <?php echo date('d/m/Y', strtotime($app['applied_at'])); ?></p>
                        </div>
                        <div class="application-status status-<?php echo $app['status'] ?? 'pending'; ?>">
                            <?php 
                            $status_text = [
                                'pending' => 'En attente',
                                'accepted' => 'Acceptée',
                                'rejected' => 'Rejetée'
                            ];
                            echo $status_text[$app['status'] ?? 'pending'];
                            ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Recommended Jobs -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2><i class="fas fa-star"></i> Emplois recommandés</h2>
                </div>
                <div class="section-content">
                    <div class="job-grid">
                        <?php foreach ($recommended_jobs as $job): ?>
                        <div class="job-card">
                            <h4><?php echo htmlspecialchars($job['title']); ?></h4>
                            <div class="company"><?php echo htmlspecialchars($job['company_name']); ?></div>
                            <div class="job-meta">
                                <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($job['location']); ?></span>
                                <span><i class="fas fa-clock"></i> <?php echo strtoupper($job['job_type']); ?></span>
                            </div>
                            <p><?php echo substr(strip_tags($job['description']), 0, 100); ?>...</p>
                            <a href="job-details.php?id=<?php echo $job['id']; ?>" class="btn-apply">
                                <i class="fas fa-eye"></i> Voir l'offre
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
