<?php
/**
 * Project Cleanup Script
 * Removes all testing and diagnostic files
 */

echo "🧹 CLEANING UP PROJECT FILES\n";
echo "============================\n";

// List of files to remove
$files_to_remove = [
    // Testing files
    'test_website_curl.php',
    'curl_diagnose_live.php',
    'clear_cache_and_test.php',
    'diagnose_live_website.php',
    'db_connection_test.php',
    'config_verify.php',
    'emergency_config_fix.php',
    'fix_database_config.php',
    'fix_live_config.php',
    'test_current_db.php',
    'test_db_connection.php',
    'verify_live_website.php',
    'verify_setup.php',
    'create_database_user.php',
    'create_new_database.php',
    'quick_database_setup.php',
    'db_test_temp.php',
    
    // Setup files (keep auto_setup.php)
    'setup_database.php',
    'update_config.php',
    'migrate_to_new_database.php',
    'create_admin_dashboard.php',
    
    // Documentation files (optional - remove if you want)
    'SEO_IMPLEMENTATION_COMPLETE.md',
    'PLATFORM_ENHANCEMENT_SUGGESTIONS.md',
    'LIVE_DEPLOYMENT_CHECKLIST.md',
    'LIVE_WEBSITE_COMPLETE.md',
    'COMPLETE_SETUP_GUIDE.md',
    
    // Temporary files
    'config/setup_completed.txt'
];

$removed_count = 0;
$not_found_count = 0;

foreach ($files_to_remove as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "✅ Removed: $file\n";
            $removed_count++;
        } else {
            echo "❌ Failed to remove: $file\n";
        }
    } else {
        $not_found_count++;
    }
}

echo "\n📊 CLEANUP SUMMARY\n";
echo "==================\n";
echo "✅ Files removed: $removed_count\n";
echo "ℹ️ Files not found: $not_found_count\n";

// Clean up any temporary directories
$temp_dirs = ['logs', 'cache', 'uploads/temp'];
foreach ($temp_dirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "✅ Cleaned directory: $dir\n";
    }
}

echo "\n🎉 PROJECT CLEANUP COMPLETED!\n";
echo "Remaining core files:\n";
echo "✅ auto_setup.php (main setup script)\n";
echo "✅ cleanup_project.php (this file)\n";
echo "✅ All website files (index.php, jobs.php, etc.)\n";
echo "✅ SEO system (includes/seo.php)\n";
echo "✅ Admin dashboard files\n";
?>
