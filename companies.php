<?php
require_once 'config/database.php';

// Get all active companies
$companies = $database->fetchAll("
    SELECT c.*, COUNT(j.id) as job_count
    FROM companies c
    LEFT JOIN jobs j ON c.id = j.company_id AND j.is_active = 1
    WHERE c.is_active = 1
    GROUP BY c.id
    ORDER BY c.name ASC
");

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis', 'ariana' => 'Ariana', 'ben_arous' => 'Ben Arous', 'manouba' => 'Manouba',
    'nabeul' => 'Nabeul', 'zaghouan' => 'Zaghouan', 'bizerte' => 'Bizerte', 'beja' => 'Béja',
    'jendouba' => 'Jendouba', 'kef' => 'Le Kef', 'siliana' => 'Siliana', 'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine', 'sidi_bouzid' => 'Sidi Bouzid', 'sousse' => 'Sousse',
    'monastir' => 'Monastir', 'mahdia' => 'Mahdia', 'sfax' => 'Sfax', 'gabes' => '<PERSON><PERSON><PERSON>',
    'medenine' => 'Médenine', 'tataouine' => 'Tataouine', 'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur', 'kebili' => 'Kébili'
];

$industries = [
    'Technology' => 'Technologie',
    'Finance' => 'Finance',
    'Healthcare' => 'Santé',
    'Education' => 'Éducation',
    'Manufacturing' => 'Industrie',
    'Retail' => 'Commerce de détail',
    'Construction' => 'Construction',
    'Transportation' => 'Transport',
    'Tourism' => 'Tourisme',
    'Agriculture' => 'Agriculture',
    'Energy' => 'Énergie',
    'Telecommunications' => 'Télécommunications',
    'Media' => 'Médias',
    'Consulting' => 'Conseil',
    'Other' => 'Autre'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Entreprises - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .companies-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .page-header {
            background: white;
            padding: 40px 0;
            margin-bottom: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .companies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .company-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .company-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .company-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .company-logo {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            margin-right: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
        
        .company-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .company-info h3 {
            color: #2c5aa0;
            margin-bottom: 5px;
            font-size: 1.3rem;
        }
        
        .company-meta {
            color: #666;
            font-size: 0.9rem;
        }
        
        .company-meta span {
            margin-right: 15px;
        }
        
        .company-description {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .company-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .job-count {
            background: #2c5aa0;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .view-jobs {
            color: #2c5aa0;
            text-decoration: none;
            font-weight: 600;
        }
        
        .view-jobs:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .companies-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .company-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="jobs.php" class="nav-link">Emplois</a></li>
                        <li><a href="companies.php" class="nav-link active">Entreprises</a></li>
                        <li><a href="about.php" class="nav-link">À propos</a></li>
                        <li><a href="contact.php" class="nav-link">Contact</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                            <?php if ($_SESSION['user_type'] === 'company'): ?>
                                <a href="company-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-building"></i> Tableau de bord
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php else: ?>
                                <a href="user-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-user"></i> Mon Profil
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="login.php" class="btn btn-outline">Connexion</a>
                            <a href="register-type.php" class="btn btn-primary">S'inscrire</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="companies-container">
        <div class="page-header">
            <div class="container">
                <h1 style="color: #2c5aa0; margin: 0;">Entreprises partenaires</h1>
                <p style="color: #666; margin: 10px 0 0 0;">Découvrez les entreprises qui recrutent en Tunisie</p>
            </div>
        </div>

        <div class="container">
            <?php if (empty($companies)): ?>
                <div style="text-align: center; padding: 60px 20px;">
                    <i class="fas fa-building" style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                    <h3 style="color: #666;">Aucune entreprise trouvée</h3>
                    <p style="color: #999;">Les entreprises apparaîtront ici une fois qu'elles seront approuvées.</p>
                </div>
            <?php else: ?>
                <div class="companies-grid">
                    <?php foreach ($companies as $company): ?>
                        <div class="company-card" onclick="window.location.href='jobs.php?company=<?php echo $company['id']; ?>'">
                            <div class="company-header">
                                <div class="company-logo">
                                    <?php if ($company['logo']): ?>
                                        <img src="uploads/logos/<?php echo $company['logo']; ?>" alt="<?php echo htmlspecialchars($company['name']); ?>">
                                    <?php else: ?>
                                        <i class="fas fa-building" style="font-size: 2rem; color: #ccc;"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="company-info">
                                    <h3><?php echo htmlspecialchars($company['name']); ?></h3>
                                    <div class="company-meta">
                                        <?php if ($company['industry']): ?>
                                            <span><i class="fas fa-industry"></i> <?php echo $industries[$company['industry']] ?? $company['industry']; ?></span>
                                        <?php endif; ?>
                                        <?php if ($company['governorate']): ?>
                                            <span><i class="fas fa-map-marker-alt"></i> <?php echo $governorates[$company['governorate']] ?? $company['governorate']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($company['description']): ?>
                                <div class="company-description">
                                    <p><?php echo htmlspecialchars(substr($company['description'], 0, 150)) . (strlen($company['description']) > 150 ? '...' : ''); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="company-stats">
                                <div class="job-count">
                                    <?php echo $company['job_count']; ?> offre<?php echo $company['job_count'] > 1 ? 's' : ''; ?>
                                </div>
                                <?php if ($company['job_count'] > 0): ?>
                                    <a href="jobs.php?company=<?php echo $company['id']; ?>" class="view-jobs" onclick="event.stopPropagation()">
                                        Voir les offres <i class="fas fa-arrow-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.php" class="logo">
                            <i class="fas fa-briefcase"></i>
                            <span>Concours Tunisie</span>
                        </a>
                        <p>La plateforme d'emploi leader en Tunisie</p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Liens rapides</h4>
                    <ul>
                        <li><a href="jobs.php">Emplois</a></li>
                        <li><a href="companies.php">Entreprises</a></li>
                        <li><a href="about.php">À propos</a></li>
                        <li><a href="contact.php">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Pour les candidats</h4>
                    <ul>
                        <li><a href="register-type.php">Créer un compte</a></li>
                        <li><a href="profile.php">Mon profil</a></li>
                        <li><a href="user-dashboard.php">Mes candidatures</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Pour les entreprises</h4>
                    <ul>
                        <li><a href="register-type.php">Inscription entreprise</a></li>
                        <li><a href="post-job.php">Publier une offre</a></li>
                        <li><a href="company-dashboard.php">Tableau de bord</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Concours Tunisie. Tous droits réservés.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
