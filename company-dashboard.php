<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if company is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'company') {
    header('Location: login-company.php');
    exit;
}

$company_id = $_SESSION['user_id'];
$company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$company_id]);

if (!$company) {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Get company's jobs
$jobs = $database->fetchAll("
    SELECT j.*, cat.name as category_name,
           COUNT(ja.id) as applications_count
    FROM jobs j
    LEFT JOIN job_categories cat ON j.category_id = cat.id
    LEFT JOIN job_applications ja ON j.id = ja.job_id
    WHERE j.company_id = ?
    GROUP BY j.id
    ORDER BY j.created_at DESC
", [$company_id]);

// Get recent applications
$recent_applications = $database->fetchAll("
    SELECT ja.*, j.title, u.first_name, u.last_name, u.email, u.phone
    FROM job_applications ja
    JOIN jobs j ON ja.job_id = j.id
    JOIN users u ON ja.user_id = u.id
    WHERE j.company_id = ?
    ORDER BY ja.applied_at DESC
    LIMIT 10
", [$company_id]);

// Calculate stats
$total_jobs = count($jobs);
$active_jobs = count(array_filter($jobs, fn($job) => $job['is_active']));
$total_applications = array_sum(array_column($jobs, 'applications_count'));
$pending_applications = $database->fetch("
    SELECT COUNT(*) as count
    FROM job_applications ja
    JOIN jobs j ON ja.job_id = j.id
    WHERE j.company_id = ? AND ja.status = 'pending'
", [$company_id])['count'];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Entreprise - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .dashboard-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .dashboard-header {
            background: white;
            padding: 30px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .company-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .company-details h1 {
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        
        .company-details p {
            color: #666;
        }
        
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .stat-card.jobs i { color: #2c5aa0; }
        .stat-card.applications i { color: #28a745; }
        .stat-card.pending i { color: #ffc107; }
        .stat-card.active i { color: #17a2b8; }
        
        .stat-card h3 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-card p {
            color: #666;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .section-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-header h2 {
            color: #333;
            margin: 0;
        }
        
        .section-content {
            padding: 25px;
        }
        
        .job-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .job-item:last-child {
            border-bottom: none;
        }
        
        .job-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .job-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .job-stats {
            text-align: right;
        }
        
        .job-stats .applications {
            color: #28a745;
            font-weight: 600;
        }
        
        .job-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .btn-sm {
            padding: 5px 12px;
            font-size: 0.8rem;
        }
        
        .application-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .candidate-info {
            flex: 1;
        }
        
        .candidate-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .candidate-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-reviewed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        @media (max-width: 768px) {
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .company-info {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="company-dashboard.php" class="nav-link active">Tableau de bord</a></li>
                        <li><a href="post-job.php" class="nav-link">Publier une offre</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <span style="margin-right: 15px;">Bonjour, <?php echo htmlspecialchars($company['name']); ?></span>
                        <a href="logout.php" class="btn btn-outline">Déconnexion</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="container">
                <div class="company-info">
                    <div class="company-details">
                        <h1><?php echo htmlspecialchars($company['name']); ?></h1>
                        <p>Gérez vos offres d'emploi et candidatures</p>
                    </div>
                    <div class="company-actions">
                        <a href="post-job.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            Publier une offre
                        </a>
                        <a href="company-profile.php" class="btn btn-outline">
                            <i class="fas fa-edit"></i>
                            Modifier le profil
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="dashboard-stats">
                <div class="stat-card jobs">
                    <i class="fas fa-briefcase"></i>
                    <h3><?php echo $total_jobs; ?></h3>
                    <p>Offres publiées</p>
                </div>
                <div class="stat-card active">
                    <i class="fas fa-eye"></i>
                    <h3><?php echo $active_jobs; ?></h3>
                    <p>Offres actives</p>
                </div>
                <div class="stat-card applications">
                    <i class="fas fa-users"></i>
                    <h3><?php echo $total_applications; ?></h3>
                    <p>Candidatures reçues</p>
                </div>
                <div class="stat-card pending">
                    <i class="fas fa-clock"></i>
                    <h3><?php echo $pending_applications; ?></h3>
                    <p>En attente</p>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="main-content">
                    <div class="section-card">
                        <div class="section-header">
                            <h2>Mes offres d'emploi</h2>
                            <a href="post-job.php" class="btn btn-primary">Nouvelle offre</a>
                        </div>
                        <div class="section-content">
                            <?php if (empty($jobs)): ?>
                                <div style="text-align: center; padding: 40px; color: #666;">
                                    <i class="fas fa-briefcase" style="font-size: 3rem; margin-bottom: 20px;"></i>
                                    <p>Vous n'avez pas encore publié d'offres d'emploi.</p>
                                    <a href="post-job.php" class="btn btn-primary">Publier votre première offre</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($jobs as $job): ?>
                                    <div class="job-item">
                                        <div class="job-info">
                                            <h4><?php echo htmlspecialchars($job['title']); ?></h4>
                                            <p><?php echo htmlspecialchars($job['category_name']); ?> • <?php echo htmlspecialchars($job['location']); ?></p>
                                            <p>Publié le <?php echo date('d/m/Y', strtotime($job['created_at'])); ?></p>
                                        </div>
                                        <div class="job-stats">
                                            <div class="applications"><?php echo $job['applications_count']; ?> candidatures</div>
                                            <div class="job-actions">
                                                <a href="job-applications.php?job_id=<?php echo $job['id']; ?>" class="btn btn-outline btn-sm">
                                                    <i class="fas fa-users"></i> Candidatures
                                                </a>
                                                <a href="edit-job.php?id=<?php echo $job['id']; ?>" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-edit"></i> Modifier
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="sidebar">
                    <div class="section-card">
                        <div class="section-header">
                            <h2>Candidatures récentes</h2>
                        </div>
                        <div class="section-content">
                            <?php if (empty($recent_applications)): ?>
                                <p style="color: #666; text-align: center;">Aucune candidature pour le moment.</p>
                            <?php else: ?>
                                <?php foreach ($recent_applications as $app): ?>
                                    <div class="application-item">
                                        <div class="candidate-info">
                                            <h4><?php echo htmlspecialchars($app['first_name'] . ' ' . $app['last_name']); ?></h4>
                                            <p><?php echo htmlspecialchars($app['title']); ?></p>
                                            <p><?php echo date('d/m/Y', strtotime($app['applied_at'])); ?></p>
                                        </div>
                                        <div class="status-badge status-<?php echo $app['status']; ?>">
                                            <?php
                                            $statuses = [
                                                'pending' => 'En attente',
                                                'reviewed' => 'Examinée',
                                                'shortlisted' => 'Présélectionné',
                                                'rejected' => 'Rejetée',
                                                'hired' => 'Embauché'
                                            ];
                                            echo $statuses[$app['status']] ?? $app['status'];
                                            ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
