<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if company is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'company') {
    header('Location: login-company.php');
    exit;
}

$company_id = $_SESSION['user_id'];
$company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$company_id]);

if (!$company) {
    session_destroy();
    header('Location: login.php');
    exit;
}

$errors = [];
$success = '';

if ($_POST) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $website = sanitize($_POST['website'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $industry = sanitize($_POST['industry'] ?? '');
    $company_size = sanitize($_POST['company_size'] ?? '');
    $address = sanitize($_POST['address'] ?? '');
    $city = sanitize($_POST['city'] ?? '');
    $governorate = sanitize($_POST['governorate'] ?? '');
    $founded_year = (int)($_POST['founded_year'] ?? 0);
    
    // Validation
    if (empty($name)) $errors[] = 'Le nom de l\'entreprise est requis';
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    if (empty($phone)) $errors[] = 'Le numéro de téléphone est requis';
    if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
        $errors[] = 'Format de site web invalide';
    }
    if ($founded_year > 0 && ($founded_year < 1800 || $founded_year > date('Y'))) {
        $errors[] = 'Année de fondation invalide';
    }
    
    // Check if email is already used by another company
    if (empty($errors)) {
        $existing = $database->fetch("SELECT id FROM companies WHERE email = ? AND id != ?", [$email, $company_id]);
        if ($existing) {
            $errors[] = 'Cette adresse email est déjà utilisée par une autre entreprise';
        }
    }
    
    // Handle logo upload
    $logo = $company['logo']; // Keep existing logo by default
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/logos/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        if (!is_writable($upload_dir)) {
            $errors[] = 'Le dossier de téléchargement du logo n\'est pas accessible en écriture.';
        } else {
            $file_extension = strtolower(pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
            if (in_array($file_extension, $allowed_extensions)) {
                if ($_FILES['logo']['size'] <= 5 * 1024 * 1024) { // 5MB max
                    $new_logo = uniqid() . '_' . $company_id . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_logo;
                    if (move_uploaded_file($_FILES['logo']['tmp_name'], $upload_path)) {
                        // Delete old logo if exists
                        if ($logo && file_exists($upload_dir . $logo)) {
                            unlink($upload_dir . $logo);
                        }
                        $logo = $new_logo;
                    } else {
                        $errors[] = 'Erreur lors du téléchargement du logo.';
                    }
                } else {
                    $errors[] = 'Le logo est trop volumineux (max 5MB)';
                }
            } else {
                $errors[] = 'Format d\'image non autorisé (JPG, PNG, GIF uniquement)';
            }
        }
    }
    
    if (empty($errors)) {
        $data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'website' => $website,
            'description' => $description,
            'industry' => $industry,
            'company_size' => $company_size,
            'address' => $address,
            'city' => $city,
            'governorate' => $governorate,
            'founded_year' => $founded_year > 0 ? $founded_year : null,
            'logo' => $logo
        ];
        
        try {
            // Debug: Show what we're trying to update
            echo "<!-- DEBUG: Updating company with data: " . json_encode($data) . " -->";
            
            // Check if we have any data to update
            if (empty($data)) {
                $errors[] = 'Aucune donnée à mettre à jour.';
            } else {
                $result = $database->update('companies', $data, 'id = :id', ['id' => $company_id]);
                
                // Debug: Show the result
                echo "<!-- DEBUG: Update result: " . var_export($result, true) . " -->";
                
                // The update method returns rowCount(), so 0 means no rows were affected (no changes)
                // but that's not an error - it just means the data was already the same
                if ($result !== false) {
                    $success = 'Profil de l\'entreprise mis à jour avec succès!';
                    // Update session name if changed
                    $_SESSION['user_name'] = $name;
                    // Refresh company data
                    $company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$company_id]);
                } else {
                    // Try to get the last error from error log
                    $error_log = error_get_last();
                    $error_message = 'Erreur lors de la mise à jour du profil.';
                    
                    if ($error_log && strpos($error_log['message'], 'Update error:') !== false) {
                        $error_message .= ' Détails: ' . $error_log['message'];
                    } else {
                        $error_message .= ' Veuillez vérifier vos informations ou réessayer plus tard.';
                    }
                    
                    $errors[] = $error_message;
                    
                    // Debug: Show the last executed query if available
                    if (method_exists($database, 'getLastQuery')) {
                        $last_query = $database->getLastQuery();
                        echo "<!-- DEBUG: Last query: " . $last_query . " -->";
                    }
                }
            }
        } catch (Exception $e) {
            $errors[] = 'Erreur SQL: ' . $e->getMessage();
            $errors[] = 'Fichier: ' . $e->getFile() . ' Ligne: ' . $e->getLine();
        }
    }
}

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis', 'ariana' => 'Ariana', 'ben_arous' => 'Ben Arous', 'manouba' => 'Manouba',
    'nabeul' => 'Nabeul', 'zaghouan' => 'Zaghouan', 'bizerte' => 'Bizerte', 'beja' => 'Béja',
    'jendouba' => 'Jendouba', 'kef' => 'Le Kef', 'siliana' => 'Siliana', 'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine', 'sidi_bouzid' => 'Sidi Bouzid', 'sousse' => 'Sousse',
    'monastir' => 'Monastir', 'mahdia' => 'Mahdia', 'sfax' => 'Sfax', 'gabes' => 'Gabès',
    'medenine' => 'Médenine', 'tataouine' => 'Tataouine', 'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur', 'kebili' => 'Kébili'
];

$industries = [
    'Technology' => 'Technologie',
    'Finance' => 'Finance',
    'Healthcare' => 'Santé',
    'Education' => 'Éducation',
    'Manufacturing' => 'Industrie',
    'Retail' => 'Commerce de détail',
    'Construction' => 'Construction',
    'Transportation' => 'Transport',
    'Tourism' => 'Tourisme',
    'Agriculture' => 'Agriculture',
    'Energy' => 'Énergie',
    'Telecommunications' => 'Télécommunications',
    'Media' => 'Médias',
    'Consulting' => 'Conseil',
    'Other' => 'Autre'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Entreprise - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
/* Modern Company Profile Styles */
.profile-container {
    padding-top: 100px;
    min-height: 100vh;
    background: #f8f9fa;
}
.profile-header {
    background: linear-gradient(90deg,#2563eb 0%,#1d4ed8 100%);
    padding: 36px 0 24px 0;
    margin-bottom: 30px;
    color: #fff;
    box-shadow: 0 2px 10px rgba(44,90,160,0.08);
}
.profile-header h1 {
    color: #fff;
    margin: 0;
    font-size: 2.1rem;
    font-weight: 800;
}
.profile-header p {
    color: #e0e7ef;
    margin: 5px 0 0 0;
}
.form-container {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 24px rgba(44,90,160,0.10);
    padding: 44px 36px;
    max-width: 800px;
    margin: 0 auto;
}
.logo-section {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}
.company-logo {
    width: 120px;
    height: 120px;
    border-radius: 16px;
    object-fit: cover;
    border: 4px solid #2563eb;
    margin-bottom: 18px;
    background: #f0f4fa;
    display: block;
}
.logo-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 16px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 18px;
    border: 4px solid #e9ecef;
    color: #b0b8c9;
    font-size: 3rem;
}
.logo-upload-area {
    border: 2px dashed #2563eb;
    border-radius: 12px;
    padding: 18px;
    background: #f8fafc;
    margin-bottom: 10px;
    cursor: pointer;
    transition: border-color 0.2s;
}
.logo-upload-area:hover {
    border-color: #1d4ed8;
}
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}
.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s;
    font-family: inherit;
}
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}
.form-group textarea {
    min-height: 120px;
    resize: vertical;
}
.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}
.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}
.btn.btn-primary {
    background: linear-gradient(90deg,#2563eb 0%,#1d4ed8 100%);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 32px;
    font-weight: 700;
    font-size: 1.1rem;
    margin-top: 10px;
    transition: background 0.2s;
}
.btn.btn-primary:hover {
    background: linear-gradient(90deg,#1d4ed8 0%,#2563eb 100%);
    color: #fff;
}
.btn.btn-outline {
    background: #fff;
    color: #2563eb;
    border: 2px solid #2563eb;
    border-radius: 8px;
    padding: 12px 32px;
    font-weight: 700;
    font-size: 1.1rem;
    margin-top: 10px;
    transition: background 0.2s, color 0.2s;
}
.btn.btn-outline:hover {
    background: #2563eb;
    color: #fff;
}
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    .form-container {
        padding: 20px;
        margin: 0 10px;
    }
}
</style>
<script>
// Logo preview and drag-and-drop
const logoInput = document.getElementById('logo');
const logoArea = document.querySelector('.logo-upload-area');
if (logoInput && logoArea) {
    logoArea.addEventListener('click', () => logoInput.click());
    logoInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.querySelector('.company-logo');
                if (img) img.src = e.target.result;
            };
            reader.readAsDataURL(this.files[0]);
        }
    });
    logoArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        logoArea.classList.add('dragover');
    });
    logoArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        logoArea.classList.remove('dragover');
    });
    logoArea.addEventListener('drop', function(e) {
        e.preventDefault();
        logoArea.classList.remove('dragover');
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            logoInput.files = e.dataTransfer.files;
            const event = new Event('change');
            logoInput.dispatchEvent(event);
        }
    });
}
</script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="company-dashboard.php" class="nav-link">Tableau de bord</a></li>
                        <li><a href="post-job.php" class="nav-link">Publier une offre</a></li>
                        <li><a href="company-profile.php" class="nav-link active">Profil</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <span style="margin-right: 15px;">Bonjour, <?php echo htmlspecialchars($company['name']); ?></span>
                        <a href="logout.php" class="btn btn-outline">Déconnexion</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="profile-container">
        <div class="profile-header">
            <div class="container">
                <h1 style="color: #2c5aa0; margin: 0;">Profil de l'entreprise</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Gérez les informations de votre entreprise</p>
            </div>
        </div>

        <div class="container">
            <div class="form-container">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data">
                    <!-- Logo Section -->
                    <div class="logo-section">
                        <?php if ($company['logo']): ?>
                            <img src="uploads/logos/<?php echo $company['logo']; ?>" alt="Logo entreprise" class="company-logo">
                        <?php else: ?>
                            <div class="logo-placeholder">
                                <i class="fas fa-building" style="font-size: 3rem; color: #ccc;"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="form-group" style="max-width: 300px; margin: 0 auto;">
                            <label for="logo">Logo de l'entreprise</label>
                            <input type="file" id="logo" name="logo" accept=".jpg,.jpeg,.png,.gif">
                            <small style="color: #666;">JPG, PNG, GIF - Max 5MB</small>
                        </div>
                    </div>

                    <!-- Company Information -->
                    <h3 style="color: #2c5aa0; margin-bottom: 20px;"><i class="fas fa-building"></i> Informations générales</h3>
                    
                    <div class="form-group">
                        <label for="name">Nom de l'entreprise *</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($company['name'] ?? ''); ?>" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($company['email'] ?? ''); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Téléphone *</label>
                            <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($company['phone'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="website">Site web</label>
                            <input type="url" id="website" name="website" value="<?php echo htmlspecialchars($company['website'] ?? ''); ?>" placeholder="https://www.exemple.com">
                        </div>
                        <div class="form-group">
                            <label for="founded_year">Année de fondation</label>
                            <input type="number" id="founded_year" name="founded_year" value="<?php echo htmlspecialchars($company['founded_year'] ?? ''); ?>" min="1800" max="<?php echo date('Y'); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="industry">Secteur d'activité</label>
                            <select id="industry" name="industry">
                                <option value="">Choisir le secteur</option>
                                <?php foreach ($industries as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $company['industry'] === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="company_size">Taille de l'entreprise</label>
                            <select id="company_size" name="company_size">
                                <option value="">Choisir la taille</option>
                                <option value="1-10" <?php echo $company['company_size'] === '1-10' ? 'selected' : ''; ?>>1-10 employés</option>
                                <option value="11-50" <?php echo $company['company_size'] === '11-50' ? 'selected' : ''; ?>>11-50 employés</option>
                                <option value="51-200" <?php echo $company['company_size'] === '51-200' ? 'selected' : ''; ?>>51-200 employés</option>
                                <option value="201-500" <?php echo $company['company_size'] === '201-500' ? 'selected' : ''; ?>>201-500 employés</option>
                                <option value="500+" <?php echo $company['company_size'] === '500+' ? 'selected' : ''; ?>>500+ employés</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description de l'entreprise</label>
                        <textarea id="description" name="description" placeholder="Décrivez votre entreprise, vos activités, votre culture d'entreprise..."><?php echo htmlspecialchars($company['description'] ?? ''); ?></textarea>
                    </div>

                    <!-- Location Information -->
                    <h3 style="color: #2c5aa0; margin: 30px 0 20px 0;"><i class="fas fa-map-marker-alt"></i> Localisation</h3>
                    
                    <div class="form-group">
                        <label for="address">Adresse</label>
                        <textarea id="address" name="address" placeholder="Adresse complète de l'entreprise"><?php echo htmlspecialchars($company['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="city">Ville</label>
                            <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($company['city'] ?? ''); ?>" placeholder="Ex: Tunis">
                        </div>
                        <div class="form-group">
                            <label for="governorate">Gouvernorat</label>
                            <select id="governorate" name="governorate">
                                <option value="">Choisir le gouvernorat</option>
                                <?php foreach ($governorates as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $company['governorate'] === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div style="text-align: center; padding-top: 30px;">
                        <button type="submit" class="btn btn-primary" style="padding: 15px 40px; font-size: 1.1rem;">
                            <i class="fas fa-save"></i>
                            Mettre à jour le profil
                        </button>
                        <a href="company-dashboard.php" class="btn btn-outline" style="margin-left: 20px;">
                            Retour au tableau de bord
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
