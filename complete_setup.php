<?php
/**
 * COMPLETE SETUP SCRIPT - TUNISIECONCOURS.ORG
 * Sets up everything from beginning to end
 */

echo "🚀 COMPLETE SETUP FOR TUNISIECONCOURS.ORG\n";
echo "=========================================\n";
echo "Database: tunisieconcours_dirr\n";
echo "User: tunisieconcours_dirr\n";
echo "Password: &w],o=IuJmAS.ar~\n";
echo "Website: https://tunisieconcours.org/\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

// Configuration
$config = [
    'db_host' => 'localhost',
    'db_name' => 'tunisieconcours_dirr',
    'db_user' => 'tunisieconcours_dirr',
    'db_pass' => '&w],o=IuJmAS.ar~',
    'site_url' => 'https://tunisieconcours.org',
    'admin_email' => '<EMAIL>'
];

$setup_steps = [];
$errors = [];

// STEP 1: System Requirements Check
echo "🔍 STEP 1: SYSTEM REQUIREMENTS CHECK\n";
echo "====================================\n";

// PHP Version
if (version_compare(PHP_VERSION, '7.4.0') >= 0) {
    echo "✅ PHP Version: " . PHP_VERSION . "\n";
    $setup_steps[] = "PHP version check";
} else {
    $errors[] = "PHP 7.4+ required. Current: " . PHP_VERSION;
    echo "❌ PHP Version: " . PHP_VERSION . " (Too old)\n";
}

// Required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'curl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ Extension: $ext\n";
    } else {
        $errors[] = "Missing PHP extension: $ext";
        echo "❌ Extension: $ext (Missing)\n";
    }
}

// Directory permissions
$required_dirs = ['config', 'uploads', 'assets'];
foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
    if (is_writable($dir)) {
        echo "✅ Writable: $dir\n";
    } else {
        $errors[] = "Directory not writable: $dir";
        echo "❌ Not writable: $dir\n";
    }
}

if (!empty($errors)) {
    echo "\n❌ SETUP CANNOT CONTINUE\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
    exit(1);
}

echo "\n";

// STEP 2: Database Configuration
echo "🔧 STEP 2: DATABASE CONFIGURATION\n";
echo "=================================\n";

$config_content = '<?php
/**
 * Database Configuration for tunisieconcours.org
 */

define(\'DB_HOST\', \'localhost\');
define(\'DB_NAME\', \'tunisieconcours_dirr\');
define(\'DB_USER\', \'tunisieconcours_dirr\');
define(\'DB_PASS\', \'&w],o=IuJmAS.ar~\');

define(\'SITE_URL\', \'https://tunisieconcours.org\');
define(\'ADMIN_EMAIL\', \'<EMAIL>\');

class Database {
    private $connection;
    
    public function __construct() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            die("Connection error: " . $e->getMessage());
        }
    }
    
    public function getConnection() { return $this->connection; }
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            die("Query error: " . $e->getMessage());
        }
    }
    public function fetchAll($sql, $params = []) { return $this->query($sql, $params)->fetchAll(); }
    public function fetch($sql, $params = []) { return $this->query($sql, $params)->fetch(); }
    public function execute($sql, $params = []) { return $this->query($sql, $params)->rowCount(); }
    public function lastInsertId() { return $this->connection->lastInsertId(); }
    public function prepare($sql) { return $this->connection->prepare($sql); }
}

try {
    $database = new Database();
} catch (Exception $e) {
    die("Database initialization failed: " . $e->getMessage());
}
?>';

if (file_put_contents('config/database.php', $config_content)) {
    echo "✅ Created config/database.php\n";
    $setup_steps[] = "Database configuration";
} else {
    die("❌ Failed to create config file\n");
}

// STEP 3: Database Connection Test
echo "\n🗄️ STEP 3: DATABASE CONNECTION TEST\n";
echo "===================================\n";

try {
    $pdo = new PDO(
        "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4",
        $config['db_user'],
        $config['db_pass']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection: SUCCESS!\n";
    echo "   Host: {$config['db_host']}\n";
    echo "   Database: {$config['db_name']}\n";
    echo "   User: {$config['db_user']}\n";
    $setup_steps[] = "Database connection verified";
    
} catch (PDOException $e) {
    echo "❌ Database connection FAILED: " . $e->getMessage() . "\n\n";
    echo "💡 MANUAL SETUP REQUIRED:\n";
    echo "1. Create database: tunisieconcours_dirr\n";
    echo "2. Create user: tunisieconcours_dirr\n";
    echo "3. Set password: &w],o=IuJmAS.ar~\n";
    echo "4. Grant all privileges\n\n";
    echo "SQL Commands:\n";
    echo "CREATE DATABASE `tunisieconcours_dirr` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
    echo "CREATE USER 'tunisieconcours_dirr'@'localhost' IDENTIFIED BY '&w],o=IuJmAS.ar~';\n";
    echo "GRANT ALL PRIVILEGES ON `tunisieconcours_dirr`.* TO 'tunisieconcours_dirr'@'localhost';\n";
    echo "FLUSH PRIVILEGES;\n";
    exit(1);
}

echo "\n";

// STEP 4: Create Database Tables
echo "📊 STEP 4: CREATING DATABASE TABLES\n";
echo "===================================\n";

// Drop existing tables
$pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
$tables_to_drop = ['job_applications', 'saved_jobs', 'jobs', 'companies', 'users', 'job_categories', 'admin_users'];
foreach ($tables_to_drop as $table) {
    $pdo->exec("DROP TABLE IF EXISTS `$table`");
}
$pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
echo "✅ Cleaned existing tables\n";

// Create tables
$tables_sql = [
    'admin_users' => "CREATE TABLE `admin_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'job_categories' => "CREATE TABLE `job_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `slug` varchar(255) UNIQUE,
        `description` text,
        `icon` varchar(50) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'companies' => "CREATE TABLE `companies` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `email` varchar(255) NOT NULL,
        `password` varchar(255) NOT NULL,
        `description` text,
        `industry` varchar(100) DEFAULT NULL,
        `website` varchar(255) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `city` varchar(100) DEFAULT NULL,
        `governorate` varchar(100) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `is_verified` tinyint(1) DEFAULT 0,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
    
    'users' => "CREATE TABLE `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `email` varchar(255) NOT NULL,
        `password` varchar(255) NOT NULL,
        `first_name` varchar(100) NOT NULL,
        `last_name` varchar(100) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `city` varchar(100) DEFAULT NULL,
        `governorate` varchar(100) DEFAULT NULL,
        `education_level` enum('high_school','bachelor','master','phd','other') DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `email_verified` tinyint(1) DEFAULT 0,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
];

foreach ($tables_sql as $table_name => $sql) {
    $pdo->exec($sql);
    echo "✅ Created table: $table_name\n";
}

// Continue with jobs and job_applications tables
$pdo->exec("CREATE TABLE `jobs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `company_id` int(11) NOT NULL,
    `category_id` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `slug` varchar(255) UNIQUE,
    `description` text NOT NULL,
    `requirements` text,
    `job_type` enum('cdi','cdd','stage','freelance','temps_partiel') NOT NULL,
    `location` varchar(100) NOT NULL,
    `salary_min` decimal(10,2) DEFAULT NULL,
    `salary_max` decimal(10,2) DEFAULT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `is_featured` tinyint(1) DEFAULT 0,
    `views_count` int(11) DEFAULT 0,
    `published_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `company_id` (`company_id`),
    KEY `category_id` (`category_id`),
    CONSTRAINT `jobs_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
    CONSTRAINT `jobs_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
echo "✅ Created table: jobs\n";

$pdo->exec("CREATE TABLE `job_applications` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `job_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `cover_letter` text NOT NULL,
    `cv_file` varchar(255) NOT NULL,
    `status` enum('pending','reviewed','hired','rejected') DEFAULT 'pending',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_application` (`job_id`,`user_id`),
    CONSTRAINT `job_applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
    CONSTRAINT `job_applications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
echo "✅ Created table: job_applications\n";

$setup_steps[] = "Database tables created";

// STEP 5: Insert Sample Data
echo "\n📊 STEP 5: INSERTING SAMPLE DATA\n";
echo "================================\n";

// Admin user
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$pdo->exec("INSERT INTO `admin_users` (`username`, `email`, `password`, `full_name`, `role`) VALUES ('admin', '<EMAIL>', '$admin_password', 'Administrateur Principal', 'super_admin')");
echo "✅ Admin user created (admin/admin123)\n";

// Job categories
$pdo->exec("
    INSERT INTO `job_categories` (`name`, `slug`, `description`, `icon`) VALUES
    ('Informatique et Technologies', 'informatique-et-technologies', 'Développement, programmation, IT, systèmes', 'fas fa-laptop-code'),
    ('Finance et Comptabilité', 'finance-comptabilite', 'Comptabilité, finance, audit, banque', 'fas fa-calculator'),
    ('Marketing et Communication', 'marketing-communication', 'Marketing digital, communication, publicité', 'fas fa-bullhorn'),
    ('Ressources Humaines', 'ressources-humaines', 'RH, recrutement, formation', 'fas fa-users'),
    ('Vente et Commerce', 'vente-commerce', 'Vente, commerce, relation client', 'fas fa-handshake'),
    ('Ingénierie', 'ingenierie', 'Ingénierie civile, mécanique, électrique', 'fas fa-cogs'),
    ('Santé et Médical', 'sante-medical', 'Médecine, pharmacie, soins', 'fas fa-heartbeat'),
    ('Éducation et Formation', 'education-formation', 'Enseignement, formation, éducation', 'fas fa-graduation-cap'),
    ('Juridique', 'juridique', 'Droit, juridique, notariat', 'fas fa-gavel'),
    ('Transport et Logistique', 'transport-logistique', 'Transport, logistique, supply chain', 'fas fa-truck')
");
echo "✅ Job categories created (10 categories)\n";

// Sample companies
$company_password = password_hash('company123', PASSWORD_DEFAULT);
$pdo->exec("
    INSERT INTO `companies` (`name`, `email`, `password`, `description`, `industry`, `website`, `phone`, `city`, `governorate`, `is_verified`) VALUES
    ('TechnoSoft Tunisia', '<EMAIL>', '$company_password', 'Société spécialisée dans le développement de solutions logicielles innovantes pour les entreprises tunisiennes et internationales.', 'Informatique', 'https://technosoft.tn', '+216 71 123 456', 'Tunis', 'tunis', 1),
    ('Banque Centrale de Tunisie', '<EMAIL>', '$company_password', 'Institution financière centrale de la République Tunisienne, responsable de la politique monétaire et de la supervision bancaire.', 'Finance', 'https://bct.gov.tn', '+216 71 340 000', 'Tunis', 'tunis', 1),
    ('Digital Marketing Pro', '<EMAIL>', '$company_password', 'Agence de marketing digital spécialisée dans les stratégies de communication en ligne et le développement de marques.', 'Marketing', 'https://digitalmarketingpro.tn', '+216 70 987 654', 'Tunis', 'tunis', 1)
");
echo "✅ Sample companies created (3 companies)\n";

// Sample users
$user_password = password_hash('user123', PASSWORD_DEFAULT);
$pdo->exec("
    INSERT INTO `users` (`email`, `password`, `first_name`, `last_name`, `phone`, `city`, `governorate`, `education_level`, `email_verified`) VALUES
    ('<EMAIL>', '$user_password', 'Ahmed', 'Ben Ali', '+216 20 123 456', 'Tunis', 'tunis', 'master', 1),
    ('<EMAIL>', '$user_password', 'Fatma', 'Trabelsi', '+216 25 987 654', 'Sfax', 'sfax', 'bachelor', 1),
    ('<EMAIL>', '$user_password', 'Mohamed', 'Karray', '+216 22 555 777', 'Sousse', 'sousse', 'master', 1)
");
echo "✅ Sample users created (3 users)\n";

// Sample jobs with SEO-friendly slugs
$pdo->exec("
    INSERT INTO `jobs` (`company_id`, `category_id`, `title`, `slug`, `description`, `requirements`, `job_type`, `location`, `salary_min`, `salary_max`, `is_active`, `is_featured`, `views_count`, `published_at`) VALUES
    (1, 1, 'Développeur Full Stack', 'developpeur-full-stack-tunis', 'Nous recherchons un développeur full stack expérimenté pour rejoindre notre équipe dynamique. Vous travaillerez sur des projets innovants utilisant les dernières technologies web.', 'Maîtrise de PHP, JavaScript, MySQL\nExpérience avec Laravel et Vue.js\nConnaissance des API REST\nMaîtrise de Git', 'cdi', 'Tunis', 1200.00, 1800.00, 1, 1, 156, NOW()),
    (2, 2, 'Comptable Senior', 'comptable-senior-tunis', 'Poste de comptable senior au sein de la Banque Centrale de Tunisie. Responsabilité de la gestion comptable et du reporting financier.', 'Diplôme en comptabilité ou finance\nExpérience minimum 5 ans\nMaîtrise des normes comptables tunisiennes\nExcellente maîtrise d\\'Excel', 'cdi', 'Tunis', 1500.00, 2200.00, 1, 1, 89, NOW()),
    (3, 3, 'Chef de Projet Digital', 'chef-projet-digital-tunis', 'Nous cherchons un chef de projet digital créatif pour gérer nos campagnes marketing et développer notre présence en ligne.', 'Expérience en marketing digital\nMaîtrise des outils Google (Analytics, Ads)\nConnaissance des réseaux sociaux\nCapacités de gestion d\\'équipe', 'cdi', 'Tunis', 1000.00, 1500.00, 1, 0, 67, NOW()),
    (1, 1, 'Développeur Mobile', 'developpeur-mobile-tunis', 'Développement d\\'applications mobiles natives et hybrides pour iOS et Android.', 'Swift, Kotlin, React Native\nExpérience avec les stores\nConnaissances UI/UX', 'cdi', 'Tunis', 1100.00, 1600.00, 1, 0, 45, NOW()),
    (2, 2, 'Analyste Financier', 'analyste-financier-tunis', 'Analyse des marchés financiers et support aux décisions d\\'investissement.', 'Master en finance\nCertifications CFA appréciées\nMaîtrise des outils d\\'analyse', 'cdi', 'Tunis', 1300.00, 1900.00, 1, 0, 32, NOW())
");
echo "✅ Sample jobs created (5 jobs with SEO URLs)\n";

$setup_steps[] = "Sample data inserted";

// STEP 6: Security Setup
echo "\n🔒 STEP 6: SECURITY SETUP\n";
echo "========================\n";

// Create .htaccess for config directory
$config_htaccess = "Order Deny,Allow\nDeny from all\n";
if (file_put_contents('config/.htaccess', $config_htaccess)) {
    echo "✅ Protected config directory\n";
}

// Create .htaccess for uploads directory
if (!is_dir('uploads')) {
    mkdir('uploads', 0755, true);
}
$uploads_htaccess = "Options -ExecCGI\nAddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\nOptions -Indexes\n";
if (file_put_contents('uploads/.htaccess', $uploads_htaccess)) {
    echo "✅ Protected uploads directory\n";
}

// Create robots.txt
$robots_content = "User-agent: *\nAllow: /\n\nSitemap: https://tunisieconcours.org/sitemap.php\n";
if (file_put_contents('robots.txt', $robots_content)) {
    echo "✅ Created robots.txt\n";
}

$setup_steps[] = "Security measures implemented";

// STEP 7: Test Live Website
echo "\n🌐 STEP 7: TESTING LIVE WEBSITE\n";
echo "===============================\n";

$test_urls = [
    'https://tunisieconcours.org/' => 'Homepage',
    'https://tunisieconcours.org/jobs.php' => 'Jobs Listing',
    'https://tunisieconcours.org/login.php' => 'Login Page',
    'https://tunisieconcours.org/admin-login.php' => 'Admin Login'
];

$website_working = true;

foreach ($test_urls as $url => $name) {
    echo "Testing: $name\n";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Setup Test)');
    curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $totalTime = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        echo "❌ cURL Error: $error\n";
        $website_working = false;
    } elseif ($httpCode === 200) {
        echo "✅ Status: $httpCode (Load time: {$totalTime}s)\n";

        // Check for database errors
        if (strpos($response, "Access denied for user 'root'") !== false) {
            echo "❌ Still using root user - config not updated on server\n";
            $website_working = false;
        } elseif (strpos($response, 'Connection error:') !== false) {
            echo "❌ Database connection error detected\n";
            $website_working = false;
        } elseif (strpos($response, 'Fatal error') !== false) {
            echo "❌ PHP Fatal error detected\n";
            $website_working = false;
        } else {
            echo "✅ No errors detected\n";
        }
    } else {
        echo "❌ HTTP Status: $httpCode\n";
        if ($httpCode !== 404) { // 404 is acceptable for some pages
            $website_working = false;
        }
    }
    echo "\n";
}

if ($website_working) {
    $setup_steps[] = "Live website tested successfully";
} else {
    echo "⚠️ Some website issues detected - may need manual configuration\n";
}

// STEP 8: Final Verification
echo "✅ STEP 8: FINAL VERIFICATION\n";
echo "=============================\n";

// Database statistics
$stats = [
    'admin_users' => $pdo->query("SELECT COUNT(*) FROM admin_users")->fetchColumn(),
    'job_categories' => $pdo->query("SELECT COUNT(*) FROM job_categories")->fetchColumn(),
    'companies' => $pdo->query("SELECT COUNT(*) FROM companies")->fetchColumn(),
    'users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'jobs' => $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn()
];

echo "📊 Database Statistics:\n";
foreach ($stats as $table => $count) {
    echo "   $table: $count records\n";
}

// Create setup completion marker
$completion_data = [
    'setup_date' => date('Y-m-d H:i:s'),
    'database' => $config['db_name'],
    'website' => $config['site_url'],
    'steps_completed' => count($setup_steps),
    'version' => '1.0'
];

if (file_put_contents('config/setup_completed.json', json_encode($completion_data, JSON_PRETTY_PRINT))) {
    echo "✅ Setup completion marker created\n";
}

$setup_steps[] = "Final verification completed";

echo "\n🎉 COMPLETE SETUP FINISHED!\n";
echo "===========================\n";

echo "📊 SETUP SUMMARY:\n";
foreach ($setup_steps as $i => $step) {
    echo "   " . ($i + 1) . ". ✅ $step\n";
}

echo "\n🔑 LOGIN CREDENTIALS:\n";
echo "====================\n";
echo "🔐 Admin Dashboard:\n";
echo "   URL: https://tunisieconcours.org/admin-login.php\n";
echo "   Username: admin\n";
echo "   Password: admin123\n\n";

echo "🏢 Sample Company:\n";
echo "   Email: <EMAIL>\n";
echo "   Password: company123\n\n";

echo "👤 Sample User:\n";
echo "   Email: <EMAIL>\n";
echo "   Password: user123\n\n";

echo "🌐 WEBSITE URLS:\n";
echo "===============\n";
echo "🏠 Homepage: https://tunisieconcours.org/\n";
echo "💼 Jobs: https://tunisieconcours.org/jobs.php\n";
echo "📝 Login: https://tunisieconcours.org/login.php\n";
echo "📋 Register: https://tunisieconcours.org/register-type.php\n";
echo "🔧 Admin: https://tunisieconcours.org/admin-login.php\n";
echo "🔍 Sitemap: https://tunisieconcours.org/sitemap.php\n\n";

echo "📄 SAMPLE JOB URLS (SEO-FRIENDLY):\n";
echo "==================================\n";
echo "🔗 https://tunisieconcours.org/emploi/developpeur-full-stack-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/comptable-senior-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/chef-projet-digital-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/developpeur-mobile-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/analyste-financier-tunis\n\n";

echo "🚀 PLATFORM STATUS: " . ($website_working ? "PRODUCTION READY!" : "NEEDS CONFIGURATION") . "\n";
echo "🇹🇳 tunisieconcours.org Offres d'Emploi\n";
echo "Ready to dominate the Tunisian job market! 🎯\n\n";

echo "⏰ Setup completed at: " . date('Y-m-d H:i:s') . "\n";
echo "📧 Support: <EMAIL>\n";
?>
