<?php
/**
 * Database Configuration for tunisieconcours.org
 * UPDATED WITH NEW CREDENTIALS
 */

define('DB_HOST', 'localhost');
define('DB_NAME', 'tunisieconcours_dirr');
define('DB_USER', 'tunisieconcours_dirr');
define('DB_PASS', '&w],o=IuJmAS.ar~');

define('SITE_URL', 'https://tunisieconcours.org');
define('ADMIN_EMAIL', '<EMAIL>');

class Database {
    private $connection;
    
    public function __construct() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            die("Connection error: " . $e->getMessage());
        }
    }
    
    public function getConnection() { return $this->connection; }
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            die("Query error: " . $e->getMessage());
        }
    }
    public function fetchAll($sql, $params = []) { return $this->query($sql, $params)->fetchAll(); }
    public function fetch($sql, $params = []) { return $this->query($sql, $params)->fetch(); }
    public function execute($sql, $params = []) { return $this->query($sql, $params)->rowCount(); }
    public function lastInsertId() { return $this->connection->lastInsertId(); }
    public function prepare($sql) { return $this->connection->prepare($sql); }

    public function insert($table, $data) {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($columns), '?');

        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";

        try {
            $stmt = $this->connection->prepare($sql);
            $result = $stmt->execute(array_values($data));

            if ($result) {
                return $this->connection->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Insert error: " . $e->getMessage());
            return false;
        }
    }

    public function update($table, $data, $where, $params = []) {
        $set_clauses = [];
        $values = [];

        foreach ($data as $column => $value) {
            $set_clauses[] = "`$column` = ?";
            $values[] = $value;
        }

        $sql = "UPDATE `{$table}` SET " . implode(", ", $set_clauses) . " WHERE $where";

        // Add where parameters to values array
        foreach ($params as $param) {
            $values[] = $param;
        }

        try {
            $stmt = $this->connection->prepare($sql);
            $result = $stmt->execute($values);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Update error: " . $e->getMessage());
            return false;
        }
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM `{$table}` WHERE $where";

        try {
            $stmt = $this->connection->prepare($sql);
            $result = $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            error_log("Delete error: " . $e->getMessage());
            return false;
        }
    }
}

try {
    $database = new Database();
} catch (Exception $e) {
    die("Database initialization failed: " . $e->getMessage());
}
?>