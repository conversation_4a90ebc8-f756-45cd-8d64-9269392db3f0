<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = '';

if ($_POST) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $message = sanitize($_POST['message'] ?? '');
    
    // Validation
    if (empty($name)) $errors[] = 'Le nom est requis';
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    if (empty($subject)) $errors[] = 'Le sujet est requis';
    if (empty($message)) $errors[] = 'Le message est requis';
    
    if (empty($errors)) {
        try {
            // Use direct PDO to save contact message
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create contact_messages table if it doesn't exist
            $pdo->exec("CREATE TABLE IF NOT EXISTS contact_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) NOT NULL,
                subject VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            $sql = "INSERT INTO contact_messages (name, email, subject, message) VALUES (?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$name, $email, $subject, $message]);
            
            if ($result) {
                $success = 'Votre message a été envoyé avec succès! Nous vous répondrons dans les plus brefs délais.';
                // Clear form
                $_POST = [];
            } else {
                $errors[] = 'Erreur lors de l\'envoi du message';
            }
        } catch (Exception $e) {
            $errors[] = 'Erreur système: ' . $e->getMessage();
            error_log('Contact form error: ' . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .contact-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .page-header {
            background: white;
            padding: 40px 0;
            margin-bottom: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .contact-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            margin-bottom: 60px;
        }
        
        .contact-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .contact-info {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .contact-item i {
            font-size: 2rem;
            color: #2c5aa0;
            margin-right: 20px;
            width: 50px;
            text-align: center;
        }
        
        .contact-item-content h4 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .contact-item-content p {
            color: #666;
            margin: 0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .map-section {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .map-placeholder {
            background: #f8f9fa;
            height: 300px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .contact-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .contact-form,
            .contact-info,
            .map-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="jobs.php" class="nav-link">Emplois</a></li>
                        <li><a href="companies.php" class="nav-link">Entreprises</a></li>
                        <li><a href="about.php" class="nav-link">À propos</a></li>
                        <li><a href="contact.php" class="nav-link active">Contact</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                            <?php if ($_SESSION['user_type'] === 'company'): ?>
                                <a href="company-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-building"></i> Tableau de bord
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php else: ?>
                                <a href="user-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-user"></i> Mon Profil
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="login.php" class="btn btn-outline">Connexion</a>
                            <a href="register-type.php" class="btn btn-primary">S'inscrire</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="contact-container">
        <div class="page-header">
            <div class="container">
                <h1 style="color: #2c5aa0; margin: 0;">Contactez-nous</h1>
                <p style="color: #666; margin: 10px 0 0 0;">Nous sommes là pour vous aider. N'hésitez pas à nous contacter!</p>
            </div>
        </div>

        <div class="container">
            <div class="contact-content">
                <!-- Contact Form -->
                <div class="contact-form">
                    <h2 style="color: #2c5aa0; margin-bottom: 30px;">Envoyez-nous un message</h2>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-error">
                            <ul style="margin: 0; padding-left: 20px;">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="form-group">
                            <label for="name">Nom complet *</label>
                            <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">Sujet *</label>
                            <select id="subject" name="subject" required>
                                <option value="">Choisir le sujet</option>
                                <option value="Support technique" <?php echo ($_POST['subject'] ?? '') === 'Support technique' ? 'selected' : ''; ?>>Support technique</option>
                                <option value="Question générale" <?php echo ($_POST['subject'] ?? '') === 'Question générale' ? 'selected' : ''; ?>>Question générale</option>
                                <option value="Partenariat" <?php echo ($_POST['subject'] ?? '') === 'Partenariat' ? 'selected' : ''; ?>>Partenariat</option>
                                <option value="Signaler un problème" <?php echo ($_POST['subject'] ?? '') === 'Signaler un problème' ? 'selected' : ''; ?>>Signaler un problème</option>
                                <option value="Autre" <?php echo ($_POST['subject'] ?? '') === 'Autre' ? 'selected' : ''; ?>>Autre</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" placeholder="Décrivez votre demande en détail..." required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-paper-plane"></i>
                            Envoyer le message
                        </button>
                    </form>
                </div>
                
                <!-- Contact Info -->
                <div class="contact-info">
                    <h2 style="color: #2c5aa0; margin-bottom: 30px;">Nos coordonnées</h2>
                    
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div class="contact-item-content">
                            <h4>Adresse</h4>
                            <p>Avenue Habib Bourguiba<br>1000 Tunis, Tunisie</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div class="contact-item-content">
                            <h4>Téléphone</h4>
                            <p>+216 71 123 456</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div class="contact-item-content">
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <div class="contact-item-content">
                            <h4>Horaires d'ouverture</h4>
                            <p>Lundi - Vendredi: 8h00 - 17h00<br>Samedi: 8h00 - 12h00</p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <h4 style="color: #333; margin-bottom: 15px;">Suivez-nous</h4>
                        <div style="display: flex; gap: 15px;">
                            <a href="#" style="color: #2c5aa0; font-size: 1.5rem;"><i class="fab fa-facebook"></i></a>
                            <a href="#" style="color: #2c5aa0; font-size: 1.5rem;"><i class="fab fa-twitter"></i></a>
                            <a href="#" style="color: #2c5aa0; font-size: 1.5rem;"><i class="fab fa-linkedin"></i></a>
                            <a href="#" style="color: #2c5aa0; font-size: 1.5rem;"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Map Section -->
            <div class="map-section">
                <h2 style="color: #2c5aa0; margin-bottom: 20px;">Notre localisation</h2>
                <p style="color: #666;">Retrouvez-nous au cœur de Tunis</p>
                <div class="map-placeholder">
                    <div style="text-align: center;">
                        <i class="fas fa-map" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                        <p>Carte interactive disponible prochainement</p>
                        <small style="color: #999;">Avenue Habib Bourguiba, 1000 Tunis</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.php" class="logo">
                            <i class="fas fa-briefcase"></i>
                            <span>Concours Tunisie</span>
                        </a>
                        <p>La plateforme d'emploi leader en Tunisie</p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Liens rapides</h4>
                    <ul>
                        <li><a href="jobs.php">Emplois</a></li>
                        <li><a href="companies.php">Entreprises</a></li>
                        <li><a href="about.php">À propos</a></li>
                        <li><a href="contact.php">Contact</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Pour les candidats</h4>
                    <ul>
                        <li><a href="register-type.php">Créer un compte</a></li>
                        <li><a href="profile.php">Mon profil</a></li>
                        <li><a href="user-dashboard.php">Mes candidatures</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Pour les entreprises</h4>
                    <ul>
                        <li><a href="register-type.php">Inscription entreprise</a></li>
                        <li><a href="post-job.php">Publier une offre</a></li>
                        <li><a href="company-dashboard.php">Tableau de bord</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Concours Tunisie. Tous droits réservés.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
