-- Sample data for Concours Tunisie Job Platform
-- Run this after creating the schema

USE concours_tunisie;

-- Insert sample companies
INSERT INTO companies (name, email, password, phone, website, description, industry, company_size, address, city, governorate, founded_year, is_verified, is_active) VALUES
('شركة التكنولوجيا المتقدمة', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.techadvanced.tn', 'شركة رائدة في مجال تطوير البرمجيات والحلول التقنية', 'Technology', '51-200', 'شارع الحبيب بورقيبة، تونس', 'تونس', 'tunis', 2010, TRUE, TRUE),

('مكتب المحاسبة الدولي', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.accounting-intl.tn', 'مكتب محاسبة معتمد يقدم خدمات المحاسبة والاستشارات المالية', 'Finance', '11-50', 'شارع فرحات حشاد، صفاقس', 'صفاقس', 'sfax', 2005, TRUE, TRUE),

('وكالة الإبداع للتسويق', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.creative-marketing.tn', 'وكالة تسويق رقمي متخصصة في الحملات الإعلانية والتسويق الإلكتروني', 'Marketing', '11-50', 'شارع الجمهورية، سوسة', 'سوسة', 'sousse', 2015, TRUE, TRUE),

('مجموعة الهندسة الحديثة', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.modern-eng.tn', 'مجموعة هندسية متخصصة في المشاريع الكبرى والاستشارات الهندسية', 'Engineering', '201-500', 'المنطقة الصناعية، أريانة', 'أريانة', 'ariana', 2000, TRUE, TRUE),

('عيادة النور الطبية', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '72345678', 'www.nour-clinic.tn', 'عيادة طبية متعددة التخصصات تقدم خدمات صحية شاملة', 'Healthcare', '51-200', 'شارع الاستقلال، نابل', 'نابل', 'nabeul', 2012, TRUE, TRUE);

-- Insert sample jobs
INSERT INTO jobs (company_id, category_id, title, description, requirements, benefits, salary_min, salary_max, job_type, experience_level, education_level, location, governorate, remote_work, application_deadline, is_featured, is_active) VALUES

(1, 1, 'مطور ويب Full Stack', 
'نبحث عن مطور ويب محترف للانضمام إلى فريقنا المتميز. المرشح المثالي يجب أن يكون لديه خبرة في تطوير التطبيقات الويب باستخدام التقنيات الحديثة.

المسؤوليات:
- تطوير وصيانة تطبيقات الويب
- العمل مع قواعد البيانات
- التعاون مع فريق التصميم
- كتابة كود نظيف وقابل للصيانة',

'المتطلبات:
- خبرة لا تقل عن 3 سنوات في تطوير الويب
- إتقان PHP, JavaScript, HTML, CSS
- خبرة في MySQL وقواعد البيانات
- معرفة بـ Git وأدوات التطوير
- القدرة على العمل ضمن فريق',

'المزايا:
- راتب تنافسي
- تأمين صحي شامل
- بيئة عمل مريحة
- فرص التطوير المهني
- مرونة في أوقات العمل',

1200, 1800, 'cdi', 'mid', 'bachelor', 'Tunis', 'tunis', FALSE, '2024-08-15', TRUE, TRUE),

(2, 2, 'محاسب أول', 
'مطلوب محاسب خبير للعمل في مكتب محاسبة رائد. المرشح المناسب يجب أن يكون لديه خبرة واسعة في المحاسبة والتدقيق.

المسؤوليات:
- إعداد القوائم المالية
- مراجعة الحسابات
- إعداد التقارير المالية
- متابعة الضرائب والرسوم',

'المتطلبات:
- شهادة في المحاسبة أو المالية
- خبرة لا تقل عن 5 سنوات
- إتقان برامج المحاسبة
- معرفة بالقوانين المالية التونسية
- دقة في العمل والالتزام بالمواعيد',

'المزايا:
- راتب مجزي
- بدلات إضافية
- تأمين صحي
- إجازات مدفوعة الأجر',

1000, 1400, 'cdi', 'senior', 'bachelor', 'Sfax', 'sfax', FALSE, '2024-08-20', TRUE, TRUE),

(3, 3, 'مدير تسويق رقمي', 
'نبحث عن مدير تسويق رقمي مبدع وخبير لقيادة استراتيجيات التسويق الإلكتروني لعملائنا.

المسؤوليات:
- وضع استراتيجيات التسويق الرقمي
- إدارة الحملات الإعلانية
- تحليل البيانات والنتائج
- إدارة وسائل التواصل الاجتماعي',

'المتطلبات:
- خبرة في التسويق الرقمي لا تقل عن 4 سنوات
- إتقان Google Ads و Facebook Ads
- معرفة بأدوات التحليل
- مهارات قيادية وإبداعية
- إجادة اللغة الإنجليزية',

'المزايا:
- راتب ممتاز
- عمولات على النتائج
- تدريب مستمر
- بيئة عمل إبداعية',

1300, 1700, 'cdi', 'mid', 'bachelor', 'Sousse', 'sousse', TRUE, '2024-08-25', TRUE, TRUE),

(4, 4, 'مهندس مدني', 
'مطلوب مهندس مدني للعمل في مشاريع البناء والتطوير العقاري.

المسؤوليات:
- الإشراف على المشاريع الإنشائية
- إعداد المخططات والتصاميم
- متابعة تنفيذ المشاريع
- ضمان الجودة والسلامة',

'المتطلبات:
- شهادة هندسة مدنية
- خبرة لا تقل عن 2 سنة
- معرفة ببرامج التصميم الهندسي
- القدرة على العمل تحت الضغط
- رخصة قيادة',

'المزايا:
- راتب تنافسي
- تأمين شامل
- سيارة خدمة
- فرص الترقي',

1100, 1500, 'cdi', 'entry', 'bachelor', 'Ariana', 'ariana', FALSE, '2024-09-01', FALSE, TRUE),

(5, 5, 'ممرض/ممرضة',
'مطلوب ممرض أو ممرضة للعمل في عيادة طبية متخصصة.

المسؤوليات:
- تقديم الرعاية التمريضية للمرضى
- مساعدة الأطباء في الفحوصات
- إعداد التقارير الطبية
- متابعة حالة المرضى',

'المتطلبات:
- شهادة في التمريض
- ترخيص مزاولة المهنة
- خبرة سابقة مفضلة
- مهارات تواصل جيدة
- القدرة على العمل بنظام الورديات',

'المزايا:
- راتب مناسب
- تأمين صحي
- تدريب مستمر
- بيئة عمل مهنية',

900, 1200, 'cdd', 'entry', 'bachelor', 'Nabeul', 'nabeul', FALSE, '2024-08-30', FALSE, TRUE),

(1, 1, 'مطور تطبيقات الجوال', 
'نبحث عن مطور تطبيقات جوال ماهر للعمل على مشاريع مبتكرة.

المسؤوليات:
- تطوير تطبيقات iOS و Android
- اختبار وتحسين الأداء
- التعاون مع فريق التصميم
- صيانة التطبيقات الحالية',

'المتطلبات:
- خبرة في تطوير التطبيقات
- إتقان React Native أو Flutter
- معرفة بـ API Integration
- خبرة في النشر على المتاجر
- مهارات حل المشاكل',

'المزايا:
- راتب ممتاز
- مشاريع متنوعة
- تطوير مهني
- فريق عمل متميز',

1400, 1900, 'freelance', 'mid', 'bachelor', 'Tunis', 'tunis', TRUE, '2024-09-10', TRUE, TRUE),

(2, 2, 'مساعد محاسب', 
'فرصة ممتازة للخريجين الجدد للبدء في مجال المحاسبة.

المسؤوليات:
- إدخال البيانات المحاسبية
- إعداد الفواتير
- متابعة المدفوعات
- مساعدة في إعداد التقارير',

'المتطلبات:
- شهادة في المحاسبة أو ما يعادلها
- معرفة أساسية ببرامج المحاسبة
- دقة في العمل
- رغبة في التعلم والتطوير
- مهارات الحاسوب الأساسية',

'المزايا:
- فرصة للتعلم
- تدريب شامل
- راتب مناسب للمبتدئين
- إمكانية الترقي',

700, 900, 'stage', 'entry', 'bachelor', 'Sfax', 'sfax', FALSE, '2024-09-05', FALSE, TRUE),

(3, 3, 'أخصائي وسائل التواصل الاجتماعي', 
'مطلوب أخصائي لإدارة حسابات وسائل التواصل الاجتماعي لعملائنا.

المسؤوليات:
- إنشاء المحتوى الإبداعي
- إدارة الحسابات الاجتماعية
- التفاعل مع الجمهور
- تحليل الأداء والنتائج',

'المتطلبات:
- خبرة في إدارة وسائل التواصل
- مهارات إبداعية في المحتوى
- معرفة بأدوات التصميم
- مهارات كتابة ممتازة
- متابعة للترندات الحديثة',

'المزايا:
- بيئة عمل إبداعية
- مرونة في العمل
- فرص التطوير
- راتب تنافسي',

800, 1100, 'temps_partiel', 'entry', 'bachelor', 'Sousse', 'sousse', TRUE, '2024-08-28', FALSE, TRUE);

-- Insert job skills
INSERT INTO job_skills (job_id, skill_name, is_required) VALUES
(1, 'PHP', TRUE),
(1, 'JavaScript', TRUE),
(1, 'MySQL', TRUE),
(1, 'HTML/CSS', TRUE),
(1, 'Git', FALSE),
(2, 'محاسبة', TRUE),
(2, 'Excel', TRUE),
(2, 'SAP', FALSE),
(3, 'التسويق الرقمي', TRUE),
(3, 'Google Ads', TRUE),
(3, 'Facebook Ads', TRUE),
(3, 'Analytics', FALSE),
(4, 'AutoCAD', TRUE),
(4, 'هندسة مدنية', TRUE),
(4, 'إدارة المشاريع', FALSE),
(5, 'تمريض', TRUE),
(5, 'رعاية المرضى', TRUE),
(6, 'React Native', TRUE),
(6, 'Flutter', FALSE),
(6, 'API Integration', TRUE),
(7, 'محاسبة', TRUE),
(7, 'Excel', TRUE),
(8, 'إدارة المحتوى', TRUE),
(8, 'التصميم', FALSE),
(8, 'كتابة إبداعية', TRUE);

-- Insert sample users (job seekers) - some active, some pending approval
INSERT INTO users (first_name, last_name, email, password, phone, date_of_birth, gender, address, city, governorate, skills, experience_years, education_level, is_active, email_verified) VALUES
('أحمد', 'بن علي', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '20123456', '1990-05-15', 'male', 'Avenue de la Liberté, Tunis', 'Tunis', 'tunis', 'PHP, JavaScript, MySQL, HTML, CSS', 3, 'bachelor', TRUE, TRUE),

('فاطمة', 'الترك', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '25987654', '1992-08-22', 'female', 'Avenue de la République, Sfax', 'Sfax', 'sfax', 'Comptabilité, Excel, SAP, Gestion financière', 5, 'bachelor', TRUE, TRUE),

('محمد', 'الحداد', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '22456789', '1988-12-10', 'male', 'Avenue de l\'Indépendance, Sousse', 'Sousse', 'sousse', 'Marketing digital, Google Ads, Facebook Ads, SEO', 4, 'master', TRUE, TRUE),

('Sarah', 'Dupont', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '21456789', '1995-03-20', 'female', 'Avenue Bourguiba, Tunis', 'Tunis', 'tunis', 'Design graphique, Photoshop, Illustrator', 2, 'bachelor', FALSE, FALSE),

('Karim', 'Ben Salem', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '22789456', '1993-07-12', 'male', 'Avenue Habib Thameur, Sfax', 'Sfax', 'sfax', 'Développement web, React, Node.js', 3, 'master', FALSE, FALSE);

-- Update job view counts and application counts
UPDATE jobs SET views_count = FLOOR(RAND() * 500) + 50;
UPDATE jobs SET applications_count = FLOOR(RAND() * 20) + 1;
