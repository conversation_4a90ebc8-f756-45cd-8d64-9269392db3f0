-- Database Schema for Concours Tunisie Job Platform
-- Created: 2024

CREATE DATABASE IF NOT EXISTS concours_tunisie CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE concours_tunisie;

-- Users table (for job seekers)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female') DEFAULT 'male',
    address TEXT,
    city VARCHAR(50),
    governorate VARCHAR(50),
    profile_image VARCHAR(255),
    cv_file VARCHAR(255),
    skills TEXT,
    experience_years INT DEFAULT 0,
    education_level ENUM('high_school', 'bachelor', 'master', 'phd', 'other') DEFAULT 'bachelor',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Companies table (for employers)
CREATE TABLE companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    website VARCHAR(255),
    logo VARCHAR(255),
    description TEXT,
    industry VARCHAR(100),
    company_size ENUM('1-10', '11-50', '51-200', '201-500', '500+') DEFAULT '1-10',
    address TEXT,
    city VARCHAR(50),
    governorate VARCHAR(50),
    founded_year YEAR,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Job categories table
CREATE TABLE job_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_ar VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Jobs table
CREATE TABLE jobs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    category_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT,
    benefits TEXT,
    salary_min DECIMAL(10,2),
    salary_max DECIMAL(10,2),
    salary_currency VARCHAR(3) DEFAULT 'TND',
    job_type ENUM('cdi', 'cdd', 'stage', 'freelance', 'temps_partiel', 'interim', 'apprentissage', 'saisonnier', 'full_time', 'part_time', 'contract', 'internship') DEFAULT 'cdi',
    experience_level ENUM('entry', 'mid', 'senior', 'executive') DEFAULT 'entry',
    education_level ENUM('high_school', 'bachelor', 'master', 'phd', 'other') DEFAULT 'bachelor',
    location VARCHAR(100),
    governorate VARCHAR(50),
    remote_work BOOLEAN DEFAULT FALSE,
    application_deadline DATE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    views_count INT DEFAULT 0,
    applications_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES job_categories(id)
);

-- Job skills table (many-to-many relationship)
CREATE TABLE job_skills (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    is_required BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
);

-- Job applications table
CREATE TABLE job_applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    user_id INT NOT NULL,
    cover_letter TEXT,
    cv_file VARCHAR(255),
    status ENUM('pending', 'reviewed', 'shortlisted', 'rejected', 'hired') DEFAULT 'pending',
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    notes TEXT,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_application (job_id, user_id)
);

-- User education table
CREATE TABLE user_education (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    degree VARCHAR(100) NOT NULL,
    field_of_study VARCHAR(100),
    institution VARCHAR(200) NOT NULL,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    grade VARCHAR(20),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User experience table
CREATE TABLE user_experience (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    job_title VARCHAR(100) NOT NULL,
    company_name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User skills table
CREATE TABLE user_skills (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'intermediate',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Saved jobs table (user favorites)
CREATE TABLE saved_jobs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    job_id INT NOT NULL,
    saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    UNIQUE KEY unique_saved_job (user_id, job_id)
);

-- Job alerts table
CREATE TABLE job_alerts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    keywords VARCHAR(255),
    location VARCHAR(100),
    category_id INT,
    job_type VARCHAR(50),
    salary_min DECIMAL(10,2),
    frequency ENUM('daily', 'weekly', 'monthly') DEFAULT 'weekly',
    is_active BOOLEAN DEFAULT TRUE,
    last_sent TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES job_categories(id)
);

-- Admin users table
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default job categories
INSERT INTO job_categories (name, name_ar, slug, description, icon) VALUES
('Informatique & IT', 'تكنولوجيا المعلومات', 'informatique', 'Développement logiciel, développement web, administration système', 'fas fa-laptop-code'),
('Finance & Comptabilité', 'المالية والمحاسبة', 'finance', 'Comptabilité, banque, analyse financière', 'fas fa-calculator'),
('Marketing & Communication', 'التسويق والاتصال', 'marketing', 'Marketing digital, vente, publicité', 'fas fa-bullhorn'),
('Ingénierie', 'الهندسة', 'ingenierie', 'Génie civil, mécanique, électrique', 'fas fa-cogs'),
('Santé & Médical', 'الصحة والطب', 'sante', 'Médical, soins infirmiers, pharmaceutique', 'fas fa-heartbeat'),
('Éducation & Formation', 'التعليم والتدريب', 'education', 'Enseignement, formation, recherche académique', 'fas fa-graduation-cap'),
('Ressources Humaines', 'الموارد البشرية', 'rh', 'Gestion RH, recrutement, formation', 'fas fa-users'),
('Service Client', 'خدمة العملاء', 'service-client', 'Support, centre d\'appels, relations clients', 'fas fa-headset'),
('Juridique', 'القانون', 'juridique', 'Conseil juridique, conformité, contrats', 'fas fa-gavel'),
('Design & Créatif', 'التصميم والإبداع', 'design', 'Design graphique, UI/UX, arts créatifs', 'fas fa-palette'),
('Vente & Commercial', 'المبيعات والتجارة', 'vente', 'Vente, commerce, développement commercial', 'fas fa-handshake'),
('Production & Industrie', 'الإنتاج والصناعة', 'production', 'Production, industrie, qualité', 'fas fa-industry'),
('Logistique & Transport', 'اللوجستيات والنقل', 'logistique', 'Logistique, transport, supply chain', 'fas fa-truck');

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, email, password, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'super_admin');

-- Create indexes for better performance
CREATE INDEX idx_jobs_company ON jobs(company_id);
CREATE INDEX idx_jobs_category ON jobs(category_id);
CREATE INDEX idx_jobs_location ON jobs(governorate);
CREATE INDEX idx_jobs_active ON jobs(is_active);
CREATE INDEX idx_jobs_featured ON jobs(is_featured);
CREATE INDEX idx_jobs_created ON jobs(created_at);
CREATE INDEX idx_applications_job ON job_applications(job_id);
CREATE INDEX idx_applications_user ON job_applications(user_id);
CREATE INDEX idx_applications_status ON job_applications(status);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_companies_email ON companies(email);
CREATE INDEX idx_saved_jobs_user ON saved_jobs(user_id);

-- Create views for common queries
CREATE VIEW active_jobs AS
SELECT 
    j.*,
    c.name as company_name,
    c.logo as company_logo,
    cat.name as category_name,
    cat.name_ar as category_name_ar
FROM jobs j
JOIN companies c ON j.company_id = c.id
JOIN job_categories cat ON j.category_id = cat.id
WHERE j.is_active = TRUE 
AND c.is_active = TRUE 
AND (j.application_deadline IS NULL OR j.application_deadline >= CURDATE());

CREATE VIEW job_stats AS
SELECT 
    j.id,
    j.title,
    j.views_count,
    j.applications_count,
    COUNT(DISTINCT ja.id) as total_applications,
    COUNT(DISTINCT CASE WHEN ja.status = 'pending' THEN ja.id END) as pending_applications,
    COUNT(DISTINCT CASE WHEN ja.status = 'reviewed' THEN ja.id END) as reviewed_applications
FROM jobs j
LEFT JOIN job_applications ja ON j.id = ja.job_id
GROUP BY j.id;
