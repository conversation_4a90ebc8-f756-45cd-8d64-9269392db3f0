<?php
echo "🔍 DEBUGGING JOB URL ROUTING ISSUE\n";
echo "==================================\n\n";

// Test different URLs to see what's happening
$test_urls = [
    'http://localhost:8000/job/informatique-it-full-stack',
    'http://localhost:8000/job/register-type.php',
    'http://localhost:8000/job/test.css',
    'http://localhost:8000/job/anything.js',
    'http://localhost:8000/assets/css/style.css'
];

foreach ($test_urls as $url) {
    echo "Testing: " . $url . "\n";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
    curl_close($ch);
    
    echo "Status: " . $httpCode . "\n";
    
    if ($redirectUrl) {
        echo "Redirects to: " . $redirectUrl . "\n";
    }
    
    // Check if it's being processed by job.php
    if (strpos($response, 'job-details.php') !== false || strpos($response, 'Concours Tunisie') !== false) {
        echo "❌ PROBLEM: Being processed by job.php!\n";
    } else {
        echo "✅ Not processed by job.php\n";
    }
    
    // Check content type
    if (preg_match('/Content-Type: ([^\r\n]+)/', $response, $matches)) {
        echo "Content-Type: " . trim($matches[1]) . "\n";
    }
    
    echo "---\n\n";
}

echo "🔍 CHECKING .HTACCESS RULES:\n";
echo "============================\n";

if (file_exists('.htaccess')) {
    echo "Current .htaccess content:\n";
    echo file_get_contents('.htaccess');
    echo "\n\n";
    
    echo "❌ PROBLEM IDENTIFIED:\n";
    echo "The .htaccess rule: RewriteRule ^job/([a-zA-Z0-9\\-]+)/?$ job.php?slug=$1 [L,QSA]\n";
    echo "This matches ANY /job/anything pattern, including:\n";
    echo "- /job/register-type.php\n";
    echo "- /job/style.css\n";
    echo "- /job/script.js\n";
    echo "- /job/any-file-name\n\n";
    
    echo "🔧 SOLUTION:\n";
    echo "We need to modify the .htaccess to be more specific\n";
    echo "and only match actual job slugs, not file extensions.\n";
} else {
    echo "No .htaccess file found\n";
}
?>
