<?php
require_once 'config/database.php';

echo "🔍 PLATFORM DEBUG STATUS\n";
echo "========================\n\n";

// Check database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit;
}

// Check session
echo "\n📊 Session Status:\n";
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']) {
    echo "   ✅ User logged in\n";
    echo "   User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "\n";
    echo "   User Type: " . ($_SESSION['user_type'] ?? 'Not set') . "\n";
    echo "   User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "\n";
} else {
    echo "   ❌ No active session\n";
}

// Check database counts
echo "\n📈 Database Counts:\n";
$stats = [
    'Users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'Active Users' => $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1")->fetchColumn(),
    'Pending Users' => $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 0")->fetchColumn(),
    'Companies' => $pdo->query("SELECT COUNT(*) FROM companies")->fetchColumn(),
    'Active Companies' => $pdo->query("SELECT COUNT(*) FROM companies WHERE is_active = 1")->fetchColumn(),
    'Pending Companies' => $pdo->query("SELECT COUNT(*) FROM companies WHERE is_active = 0")->fetchColumn(),
    'Jobs' => $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn(),
    'Featured Jobs' => $pdo->query("SELECT COUNT(*) FROM jobs WHERE is_featured = 1")->fetchColumn(),
    'Applications' => $pdo->query("SELECT COUNT(*) FROM job_applications")->fetchColumn()
];

foreach ($stats as $label => $count) {
    echo "   {$label}: {$count}\n";
}

// Check active companies for login test
echo "\n🏢 Active Companies (for login testing):\n";
$companies = $pdo->query("SELECT id, name, email FROM companies WHERE is_active = 1 LIMIT 5")->fetchAll();
foreach ($companies as $company) {
    echo "   ID: {$company['id']}, Name: {$company['name']}, Email: {$company['email']}\n";
}

// Check recent jobs
echo "\n💼 Recent Jobs:\n";
$jobs = $pdo->query("SELECT id, title, company_id, is_featured FROM jobs ORDER BY created_at DESC LIMIT 5")->fetchAll();
foreach ($jobs as $job) {
    echo "   ID: {$job['id']}, Title: {$job['title']}, Company: {$job['company_id']}, Featured: " . ($job['is_featured'] ? 'Yes' : 'No') . "\n";
}

// Test homepage content
echo "\n🏠 Homepage Test:\n";
$ch = curl_init('http://localhost:8000/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   HTTP Status: {$httpCode}\n";
if (strpos($response, 'Fatal error') !== false) {
    echo "   ❌ Fatal error on homepage\n";
    preg_match('/Fatal error.*?on line (\d+)/', $response, $matches);
    if ($matches) {
        echo "   Error on line: {$matches[1]}\n";
    }
} else {
    echo "   ✅ Homepage loads without fatal errors\n";
}

if (strpos($response, 'Emplois en vedette') !== false) {
    echo "   ✅ French content present\n";
} else {
    echo "   ❌ French content missing\n";
}

echo "\n🎯 RECOMMENDATIONS:\n";
echo "1. Test login with: <EMAIL> / password123\n";
echo "2. Check if companies are active in database\n";
echo "3. Verify session handling in navigation\n";
echo "4. Test job posting functionality\n";

echo "\n✅ Debug completed!\n";
?>
