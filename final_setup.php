<?php
/**
 * FINAL SETUP SCRIPT - CLEAN VERSION
 * Sets up tunisieconcours.org with new database credentials
 */

echo "🚀 FINAL SETUP FOR TUNISIECONCOURS.ORG\n";
echo "======================================\n";
echo "NEW Database: tunisieconcours_dirr\n";
echo "NEW User: tunisieconcours_dirr\n";
echo "NEW Password: &w],o=IuJmAS.ar~\n";
echo "Website: https://tunisieconcours.org/\n\n";

// NEW Database credentials
$config = [
    'db_host' => 'localhost',
    'db_name' => 'tunisieconcours_dirr',
    'db_user' => 'tunisieconcours_dirr',
    'db_pass' => '&w],o=IuJmAS.ar~',
    'site_url' => 'https://tunisieconcours.org',
    'admin_email' => '<EMAIL>'
];

// Step 1: Update Database Configuration
echo "🔧 STEP 1: UPDATING DATABASE CONFIGURATION\n";
echo "==========================================\n";

$new_config_content = '<?php
/**
 * Database Configuration for tunisieconcours.org
 * UPDATED WITH NEW CREDENTIALS
 */

define(\'DB_HOST\', \'localhost\');
define(\'DB_NAME\', \'tunisieconcours_dirr\');
define(\'DB_USER\', \'tunisieconcours_dirr\');
define(\'DB_PASS\', \'&w],o=IuJmAS.ar~\');

define(\'SITE_URL\', \'https://tunisieconcours.org\');
define(\'ADMIN_EMAIL\', \'<EMAIL>\');

class Database {
    private $connection;
    
    public function __construct() {
        try {
            $this->connection = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            die("Connection error: " . $e->getMessage());
        }
    }
    
    public function getConnection() { return $this->connection; }
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            die("Query error: " . $e->getMessage());
        }
    }
    public function fetchAll($sql, $params = []) { return $this->query($sql, $params)->fetchAll(); }
    public function fetch($sql, $params = []) { return $this->query($sql, $params)->fetch(); }
    public function execute($sql, $params = []) { return $this->query($sql, $params)->rowCount(); }
    public function lastInsertId() { return $this->connection->lastInsertId(); }
    public function prepare($sql) { return $this->connection->prepare($sql); }
}

try {
    $database = new Database();
} catch (Exception $e) {
    die("Database initialization failed: " . $e->getMessage());
}
?>';

// Create config directory if needed
if (!is_dir('config')) {
    mkdir('config', 0755, true);
    echo "✅ Created config directory\n";
}

// Write new config
if (file_put_contents('config/database.php', $new_config_content)) {
    echo "✅ Updated config/database.php with NEW credentials\n";
} else {
    die("❌ Failed to write config file\n");
}

// Step 2: Test Database Connection
echo "\n🗄️ STEP 2: TESTING DATABASE CONNECTION\n";
echo "======================================\n";

try {
    $pdo = new PDO(
        "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4",
        $config['db_user'],
        $config['db_pass']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection: SUCCESS!\n";
    echo "   Host: {$config['db_host']}\n";
    echo "   Database: {$config['db_name']}\n";
    echo "   User: {$config['db_user']}\n";
    
} catch (PDOException $e) {
    echo "❌ Database connection FAILED: " . $e->getMessage() . "\n";
    echo "\n💡 SOLUTION:\n";
    echo "Create the database and user manually:\n";
    echo "1. Open phpMyAdmin\n";
    echo "2. Create database: tunisieconcours_dirr\n";
    echo "3. Create user: tunisieconcours_dirr with password: &w],o=IuJmAS.ar~\n";
    echo "4. Grant all privileges to the user\n";
    exit(1);
}

// Step 3: Create Tables
echo "\n📊 STEP 3: CREATING DATABASE TABLES\n";
echo "===================================\n";

// Drop existing tables
$pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
$tables_to_drop = ['job_applications', 'saved_jobs', 'jobs', 'companies', 'users', 'job_categories', 'admin_users'];
foreach ($tables_to_drop as $table) {
    $pdo->exec("DROP TABLE IF EXISTS `$table`");
}
$pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
echo "✅ Cleaned existing tables\n";

// Create tables
$tables = [
    'admin_users' => "
        CREATE TABLE `admin_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'job_categories' => "
        CREATE TABLE `job_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `slug` varchar(255) UNIQUE,
            `description` text,
            `icon` varchar(50) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'companies' => "
        CREATE TABLE `companies` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `email` varchar(255) NOT NULL,
            `password` varchar(255) NOT NULL,
            `description` text,
            `industry` varchar(100) DEFAULT NULL,
            `website` varchar(255) DEFAULT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `city` varchar(100) DEFAULT NULL,
            `governorate` varchar(100) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `is_verified` tinyint(1) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'users' => "
        CREATE TABLE `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `email` varchar(255) NOT NULL,
            `password` varchar(255) NOT NULL,
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `city` varchar(100) DEFAULT NULL,
            `governorate` varchar(100) DEFAULT NULL,
            `education_level` enum('high_school','bachelor','master','phd','other') DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `email_verified` tinyint(1) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'jobs' => "
        CREATE TABLE `jobs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `company_id` int(11) NOT NULL,
            `category_id` int(11) NOT NULL,
            `title` varchar(255) NOT NULL,
            `slug` varchar(255) UNIQUE,
            `description` text NOT NULL,
            `requirements` text,
            `job_type` enum('cdi','cdd','stage','freelance','temps_partiel') NOT NULL,
            `location` varchar(100) NOT NULL,
            `salary_min` decimal(10,2) DEFAULT NULL,
            `salary_max` decimal(10,2) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `is_featured` tinyint(1) DEFAULT 0,
            `views_count` int(11) DEFAULT 0,
            `published_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `company_id` (`company_id`),
            KEY `category_id` (`category_id`),
            CONSTRAINT `jobs_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
            CONSTRAINT `jobs_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ",
    
    'job_applications' => "
        CREATE TABLE `job_applications` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `job_id` int(11) NOT NULL,
            `user_id` int(11) NOT NULL,
            `cover_letter` text NOT NULL,
            `cv_file` varchar(255) NOT NULL,
            `status` enum('pending','reviewed','hired','rejected') DEFAULT 'pending',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_application` (`job_id`,`user_id`),
            CONSTRAINT `job_applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
            CONSTRAINT `job_applications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    "
];

foreach ($tables as $table_name => $sql) {
    $pdo->exec($sql);
    echo "✅ Created table: $table_name\n";
}

echo "\n📊 STEP 4: INSERTING SAMPLE DATA\n";
echo "================================\n";

// Insert data
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$company_password = password_hash('company123', PASSWORD_DEFAULT);
$user_password = password_hash('user123', PASSWORD_DEFAULT);

// Admin user
$pdo->exec("INSERT INTO `admin_users` (`username`, `email`, `password`, `full_name`, `role`) VALUES ('admin', '<EMAIL>', '$admin_password', 'Administrateur Principal', 'super_admin')");
echo "✅ Admin user created (admin/admin123)\n";

// Job categories
$pdo->exec("
    INSERT INTO `job_categories` (`name`, `slug`, `description`, `icon`) VALUES
    ('Informatique et Technologies', 'informatique-et-technologies', 'Développement, programmation, IT', 'fas fa-laptop-code'),
    ('Finance et Comptabilité', 'finance-comptabilite', 'Comptabilité, finance, audit', 'fas fa-calculator'),
    ('Marketing et Communication', 'marketing-communication', 'Marketing digital, communication', 'fas fa-bullhorn'),
    ('Ressources Humaines', 'ressources-humaines', 'RH, recrutement, formation', 'fas fa-users'),
    ('Vente et Commerce', 'vente-commerce', 'Vente, commerce, relation client', 'fas fa-handshake')
");
echo "✅ Job categories created\n";

// Sample companies
$pdo->exec("
    INSERT INTO `companies` (`name`, `email`, `password`, `description`, `industry`, `city`, `governorate`, `is_verified`) VALUES
    ('TechnoSoft Tunisia', '<EMAIL>', '$company_password', 'Société spécialisée dans le développement de solutions logicielles.', 'Informatique', 'Tunis', 'tunis', 1),
    ('Digital Marketing Pro', '<EMAIL>', '$company_password', 'Agence de marketing digital spécialisée.', 'Marketing', 'Tunis', 'tunis', 1)
");
echo "✅ Sample companies created\n";

// Sample users
$pdo->exec("
    INSERT INTO `users` (`email`, `password`, `first_name`, `last_name`, `city`, `governorate`, `education_level`, `email_verified`) VALUES
    ('<EMAIL>', '$user_password', 'Ahmed', 'Ben Ali', 'Tunis', 'tunis', 'master', 1),
    ('<EMAIL>', '$user_password', 'Fatma', 'Trabelsi', 'Sfax', 'sfax', 'bachelor', 1)
");
echo "✅ Sample users created\n";

// Sample jobs
$pdo->exec("
    INSERT INTO `jobs` (`company_id`, `category_id`, `title`, `slug`, `description`, `job_type`, `location`, `salary_min`, `salary_max`, `is_active`, `is_featured`, `published_at`) VALUES
    (1, 1, 'Développeur Full Stack', 'developpeur-full-stack-tunis', 'Nous recherchons un développeur full stack expérimenté.', 'cdi', 'Tunis', 1200, 1800, 1, 1, NOW()),
    (2, 3, 'Chef de Projet Digital', 'chef-projet-digital-tunis', 'Chef de projet digital créatif pour nos campagnes.', 'cdi', 'Tunis', 1000, 1500, 1, 0, NOW()),
    (1, 1, 'Développeur Mobile', 'developpeur-mobile-tunis', 'Développement d\\'applications mobiles natives.', 'cdi', 'Tunis', 1100, 1600, 1, 0, NOW())
");
echo "✅ Sample jobs created\n";

echo "\n🌐 STEP 5: TESTING LIVE WEBSITE\n";
echo "===============================\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://tunisieconcours.org/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ cURL Error: $error\n";
} elseif ($httpCode === 200) {
    echo "✅ Website Status: $httpCode (OK)\n";
    
    if (strpos($response, "Access denied for user 'root'") !== false) {
        echo "❌ Still getting root error - config not updated on server\n";
    } elseif (strpos($response, 'Connection error:') !== false) {
        echo "❌ Database connection error\n";
    } elseif (strpos($response, 'Fatal error') !== false) {
        echo "❌ PHP Fatal error\n";
    } else {
        echo "✅ Website working correctly!\n";
    }
} else {
    echo "❌ Website Status: $httpCode\n";
}

echo "\n🎉 FINAL SETUP COMPLETED!\n";
echo "=========================\n";

echo "📊 Database Statistics:\n";
$stats = [
    'admin_users' => $pdo->query("SELECT COUNT(*) FROM admin_users")->fetchColumn(),
    'job_categories' => $pdo->query("SELECT COUNT(*) FROM job_categories")->fetchColumn(),
    'companies' => $pdo->query("SELECT COUNT(*) FROM companies")->fetchColumn(),
    'users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'jobs' => $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn()
];

foreach ($stats as $table => $count) {
    echo "   $table: $count records\n";
}

echo "\n🔑 LOGIN CREDENTIALS:\n";
echo "👤 Admin: admin / admin123\n";
echo "🏢 Company: <EMAIL> / company123\n";
echo "👥 User: <EMAIL> / user123\n";

echo "\n🔗 TEST URLS:\n";
echo "🏠 Homepage: https://tunisieconcours.org/\n";
echo "💼 Jobs: https://tunisieconcours.org/jobs.php\n";
echo "🔧 Admin: https://tunisieconcours.org/admin-login.php\n";
echo "📄 Sample Job: https://tunisieconcours.org/emploi/developpeur-full-stack-tunis\n";

echo "\n✅ READY FOR PRODUCTION!\n";
echo "🇹🇳 tunisieconcours.org Offres d'Emploi\n";
?>
