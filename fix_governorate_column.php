<?php
/**
 * Database Migration Script
 * Adds missing governorate column to jobs table
 */

echo "🔧 FIXING MISSING GOVERNORATE COLUMN\n";
echo "=====================================\n";

// Database connection
require_once 'config/database.php';

try {
    // Check if governorate column exists
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM jobs LIKE 'governorate'");
    $column_exists = $stmt->rowCount() > 0;
    
    if (!$column_exists) {
        echo "❌ Governorate column missing in jobs table\n";
        echo "🔧 Adding governorate column...\n";
        
        // Add the column
        $sql = "ALTER TABLE jobs ADD COLUMN governorate VARCHAR(50) AFTER location";
        $pdo->exec($sql);
        
        echo "✅ Governorate column added successfully!\n";
        echo "📍 Column added after 'location' column\n";
    } else {
        echo "✅ Governorate column already exists\n";
    }
    
    // Verify the column was added
    $stmt = $pdo->query("SHOW COLUMNS FROM jobs LIKE 'governorate'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Verification: Governorate column is now present\n";
    } else {
        echo "❌ Error: Column was not added properly\n";
    }
    
    echo "\n🎉 MIGRATION COMPLETED SUCCESSFULLY!\n";
    echo "You can now post jobs without the SQL error.\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
}
?> 