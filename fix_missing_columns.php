<?php
/**
 * Fix Missing Database Columns
 * Adds missing columns to existing tables
 */

echo "🔧 FIXING MISSING DATABASE COLUMNS\n";
echo "==================================\n";
echo "Database: tunisieconcours_dirr\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=tunisieconcours_dirr;charset=utf8mb4",
        "tunisieconcours_dirr",
        "&w],o=IuJmAS.ar~",
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "✅ Database connection successful\n\n";
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

// Function to check if column exists
function columnExists($pdo, $table, $column) {
    try {
        $stmt = $pdo->prepare("SHOW COLUMNS FROM `$table` LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Function to check if table exists
function tableExists($pdo, $table) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

echo "🔍 CHECKING EXISTING TABLES AND COLUMNS\n";
echo "=======================================\n";

$required_tables = [
    'companies' => [
        'logo' => "varchar(255) DEFAULT NULL",
        'cover_image' => "varchar(255) DEFAULT NULL",
        'slug' => "varchar(255) DEFAULT NULL",
        'description_ar' => "text DEFAULT NULL",
        'fax' => "varchar(20) DEFAULT NULL",
        'address' => "text DEFAULT NULL",
        'postal_code' => "varchar(10) DEFAULT NULL",
        'country' => "varchar(100) DEFAULT 'Tunisia'",
        'employee_count' => "enum('1-10','11-50','51-200','201-500','501-1000','1000+') DEFAULT NULL",
        'founded_year' => "int(4) DEFAULT NULL",
        'linkedin_url' => "varchar(255) DEFAULT NULL",
        'facebook_url' => "varchar(255) DEFAULT NULL",
        'twitter_url' => "varchar(255) DEFAULT NULL",
        'instagram_url' => "varchar(255) DEFAULT NULL",
        'is_premium' => "tinyint(1) DEFAULT 0",
        'verification_date' => "timestamp NULL DEFAULT NULL",
        'jobs_count' => "int(11) DEFAULT 0",
        'views_count' => "int(11) DEFAULT 0",
        'email_verified' => "tinyint(1) DEFAULT 0",
        'email_verification_token' => "varchar(255) DEFAULT NULL",
        'password_reset_token' => "varchar(255) DEFAULT NULL",
        'password_reset_expires' => "timestamp NULL DEFAULT NULL",
        'last_login' => "timestamp NULL DEFAULT NULL",
        'updated_at' => "timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ],
    'jobs' => [
        'slug' => "varchar(255) DEFAULT NULL",
        'title_ar' => "varchar(255) DEFAULT NULL",
        'description_ar' => "text DEFAULT NULL",
        'requirements_ar' => "text DEFAULT NULL",
        'benefits' => "text DEFAULT NULL",
        'benefits_ar' => "text DEFAULT NULL",
        'responsibilities' => "text DEFAULT NULL",
        'responsibilities_ar' => "text DEFAULT NULL",
        'experience_level' => "enum('entry','mid','senior','executive') DEFAULT NULL",
        'education_level' => "enum('high_school','bachelor','master','phd','other') DEFAULT NULL",
        'remote_work' => "tinyint(1) DEFAULT 0",
        'salary_currency' => "varchar(3) DEFAULT 'TND'",
        'salary_period' => "enum('hour','day','month','year') DEFAULT 'month'",
        'salary_negotiable' => "tinyint(1) DEFAULT 0",
        'positions_available' => "int(11) DEFAULT 1",
        'contact_email' => "varchar(255) DEFAULT NULL",
        'contact_phone' => "varchar(20) DEFAULT NULL",
        'application_instructions' => "text DEFAULT NULL",
        'required_documents' => "text DEFAULT NULL",
        'is_urgent' => "tinyint(1) DEFAULT 0",
        'is_premium' => "tinyint(1) DEFAULT 0",
        'saves_count' => "int(11) DEFAULT 0",
        'shares_count' => "int(11) DEFAULT 0",
        'status' => "enum('draft','published','paused','closed','expired') DEFAULT 'published'",
        'rejection_reason' => "text DEFAULT NULL",
        'admin_notes' => "text DEFAULT NULL",
        'seo_title' => "varchar(255) DEFAULT NULL",
        'seo_description' => "text DEFAULT NULL",
        'seo_keywords' => "text DEFAULT NULL",
        'expires_at' => "timestamp NULL DEFAULT NULL",
        'featured_until' => "timestamp NULL DEFAULT NULL",
        'updated_at' => "timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ],
    'users' => [
        'date_of_birth' => "date DEFAULT NULL",
        'gender' => "enum('male','female','other') DEFAULT NULL",
        'nationality' => "varchar(100) DEFAULT 'Tunisian'",
        'address' => "text DEFAULT NULL",
        'postal_code' => "varchar(10) DEFAULT NULL",
        'country' => "varchar(100) DEFAULT 'Tunisia'",
        'avatar' => "varchar(255) DEFAULT NULL",
        'field_of_study' => "varchar(255) DEFAULT NULL",
        'university' => "varchar(255) DEFAULT NULL",
        'graduation_year' => "int(4) DEFAULT NULL",
        'current_position' => "varchar(255) DEFAULT NULL",
        'current_company' => "varchar(255) DEFAULT NULL",
        'languages' => "text DEFAULT NULL",
        'portfolio_url' => "varchar(255) DEFAULT NULL",
        'linkedin_url' => "varchar(255) DEFAULT NULL",
        'github_url' => "varchar(255) DEFAULT NULL",
        'expected_salary_min' => "decimal(10,2) DEFAULT NULL",
        'expected_salary_max' => "decimal(10,2) DEFAULT NULL",
        'availability' => "enum('immediate','1_week','2_weeks','1_month','3_months','other') DEFAULT 'immediate'",
        'job_preferences' => "text DEFAULT NULL",
        'is_public' => "tinyint(1) DEFAULT 1",
        'email_verification_token' => "varchar(255) DEFAULT NULL",
        'password_reset_token' => "varchar(255) DEFAULT NULL",
        'password_reset_expires' => "timestamp NULL DEFAULT NULL",
        'last_login' => "timestamp NULL DEFAULT NULL",
        'profile_views' => "int(11) DEFAULT 0",
        'updated_at' => "timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ],
    'job_categories' => [
        'name_ar' => "varchar(100) DEFAULT NULL",
        'description_ar' => "text DEFAULT NULL",
        'color' => "varchar(7) DEFAULT '#007bff'",
        'image' => "varchar(255) DEFAULT NULL",
        'sort_order' => "int(11) DEFAULT 0",
        'jobs_count' => "int(11) DEFAULT 0",
        'meta_title' => "varchar(255) DEFAULT NULL",
        'meta_description' => "text DEFAULT NULL",
        'updated_at' => "timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ]
];

$columns_added = 0;
$columns_existed = 0;

foreach ($required_tables as $table => $columns) {
    if (!tableExists($pdo, $table)) {
        echo "❌ Table '$table' does not exist - run full database setup first\n";
        continue;
    }
    
    echo "📊 Checking table: $table\n";
    
    foreach ($columns as $column => $definition) {
        if (!columnExists($pdo, $table, $column)) {
            try {
                $sql = "ALTER TABLE `$table` ADD COLUMN `$column` $definition";
                $pdo->exec($sql);
                echo "   ✅ Added column: $column\n";
                $columns_added++;
            } catch (Exception $e) {
                echo "   ❌ Failed to add column $column: " . $e->getMessage() . "\n";
            }
        } else {
            $columns_existed++;
        }
    }
}

echo "\n🔍 ADDING MISSING INDEXES\n";
echo "=========================\n";

$indexes = [
    'companies' => [
        'UNIQUE KEY `slug` (`slug`)',
        'KEY `is_active` (`is_active`)',
        'KEY `is_verified` (`is_verified`)',
        'KEY `governorate` (`governorate`)',
        'KEY `industry` (`industry`)'
    ],
    'jobs' => [
        'UNIQUE KEY `slug` (`slug`)',
        'KEY `location` (`location`)',
        'KEY `job_type` (`job_type`)',
        'KEY `is_featured` (`is_featured`)',
        'KEY `published_at` (`published_at`)',
        'KEY `expires_at` (`expires_at`)'
    ],
    'job_categories' => [
        'UNIQUE KEY `slug` (`slug`)',
        'KEY `is_active` (`is_active`)',
        'KEY `sort_order` (`sort_order`)'
    ]
];

foreach ($indexes as $table => $table_indexes) {
    if (tableExists($pdo, $table)) {
        foreach ($table_indexes as $index) {
            try {
                $pdo->exec("ALTER TABLE `$table` ADD $index");
                echo "✅ Added index to $table\n";
            } catch (Exception $e) {
                // Index might already exist, that's okay
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "⚠️ Index warning for $table: " . $e->getMessage() . "\n";
                }
            }
        }
    }
}

echo "\n📊 SUMMARY\n";
echo "==========\n";
echo "✅ Columns added: $columns_added\n";
echo "ℹ️ Columns already existed: $columns_existed\n";

// Test the fix by running a query that was failing
echo "\n🧪 TESTING THE FIX\n";
echo "==================\n";

try {
    $test_query = "SELECT j.*, c.name as company_name, c.logo as company_logo, cat.name as category_name
                   FROM jobs j
                   JOIN companies c ON j.company_id = c.id
                   JOIN job_categories cat ON j.category_id = cat.id
                   WHERE j.is_active = 1 AND c.is_active = 1
                   LIMIT 1";
    
    $result = $pdo->query($test_query);
    if ($result) {
        echo "✅ Test query successful - c.logo column is now working!\n";
        $job = $result->fetch();
        if ($job) {
            echo "✅ Sample job found: " . $job['title'] . "\n";
            echo "✅ Company: " . $job['company_name'] . "\n";
            echo "✅ Logo column: " . ($job['company_logo'] ? $job['company_logo'] : 'NULL') . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Test query failed: " . $e->getMessage() . "\n";
}

echo "\n🎉 COLUMN FIX COMPLETED!\n";
echo "========================\n";
echo "The 'c.logo' error should now be resolved.\n";
echo "All missing columns have been added to existing tables.\n";
echo "\n🔗 Test your website:\n";
echo "Homepage: https://tunisieconcours.org/\n";
echo "Jobs: https://tunisieconcours.org/jobs.php\n";
echo "\nTime: " . date('Y-m-d H:i:s') . "\n";
?>
