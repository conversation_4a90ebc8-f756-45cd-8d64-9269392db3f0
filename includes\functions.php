<?php

// File upload constants
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('MAX_IMAGE_SIZE', 2 * 1024 * 1024); // 2MB
define('UPLOAD_PATH_CV', 'uploads/cvs/');
define('UPLOAD_PATH_LOGOS', 'uploads/logos/');
define('UPLOAD_PATH_PROFILES', 'uploads/profiles/');
define('UPLOAD_PATH_AVATARS', 'uploads/avatars/');

/**
 * Utility Functions for Concours Tunisie
 * Common functions used throughout the application
 */

/**
 * Sanitize input data
 * @param string $data The data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }

    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Generate SEO-friendly slug from text
 * @param string $text The text to convert to slug
 * @return string SEO-friendly slug
 */
function generateSlug($text) {
    // Convert to lowercase
    $text = strtolower($text);

    // Replace accented characters
    $text = str_replace(
        ['à', 'á', 'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ð', 'ñ', 'ò', 'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'þ', 'ÿ'],
        ['a', 'a', 'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'd', 'n', 'o', 'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'th', 'y'],
        $text
    );

    // Remove special characters and replace with hyphens
    $text = preg_replace('/[^a-z0-9\s\-]/', '', $text);

    // Replace multiple spaces/hyphens with single hyphen
    $text = preg_replace('/[\s\-]+/', '-', $text);

    // Remove leading/trailing hyphens
    $text = trim($text, '-');

    // Limit length
    $text = substr($text, 0, 100);

    return $text;
}

/**
 * Validate email address
 * @param string $email The email to validate
 * @return bool True if valid, false otherwise
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Hash password securely
 * @param string $password The password to hash
 * @return string Hashed password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password against hash
 * @param string $password The plain password
 * @param string $hash The hashed password
 * @return bool True if password matches, false otherwise
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate a random token
 * @param int $length Token length (default: 32)
 * @return string Random token
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Generate a secure random string
 * @param int $length String length
 * @return string Random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * Validate phone number (Tunisian format)
 * @param string $phone The phone number to validate
 * @return bool True if valid, false otherwise
 */
function validatePhone($phone) {
    // Remove spaces and special characters
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // Tunisian phone number patterns
    $patterns = [
        '/^\+216[0-9]{8}$/',     // +216xxxxxxxx
        '/^216[0-9]{8}$/',       // 216xxxxxxxx
        '/^[0-9]{8}$/',          // xxxxxxxx
        '/^0[0-9]{7}$/',         // 0xxxxxxx
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $phone)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Format phone number to standard format
 * @param string $phone The phone number to format
 * @return string Formatted phone number
 */
function formatPhone($phone) {
    // Remove all non-numeric characters except +
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    
    // Add +216 prefix if not present
    if (!str_starts_with($phone, '+216') && !str_starts_with($phone, '216')) {
        if (str_starts_with($phone, '0')) {
            $phone = '+216' . substr($phone, 1);
        } else {
            $phone = '+216' . $phone;
        }
    } elseif (str_starts_with($phone, '216')) {
        $phone = '+' . $phone;
    }
    
    return $phone;
}

/**
 * Validate password strength
 * @param string $password The password to validate
 * @return array Array with 'valid' boolean and 'errors' array
 */
function validatePassword($password) {
    $errors = [];
    
    if (strlen($password) < 6) {
        $errors[] = 'Le mot de passe doit contenir au moins 6 caractères';
    }
    
    if (strlen($password) > 128) {
        $errors[] = 'Le mot de passe ne peut pas dépasser 128 caractères';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'Le mot de passe doit contenir au moins une lettre minuscule';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'Le mot de passe doit contenir au moins une lettre majuscule';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = 'Le mot de passe doit contenir au moins un chiffre';
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Clean and validate URL
 * @param string $url The URL to validate
 * @return string|false Cleaned URL or false if invalid
 */
function validateUrl($url) {
    if (empty($url)) {
        return false;
    }
    
    // Add http:// if no protocol specified
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    return filter_var($url, FILTER_VALIDATE_URL);
}



/**
 * Truncate text to specified length
 * @param string $text The text to truncate
 * @param int $length Maximum length
 * @param string $suffix Suffix to add if truncated
 * @return string Truncated text
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length - strlen($suffix)) . $suffix;
}

/**
 * Format date for display
 * @param string $date The date to format
 * @param string $format The format string
 * @return string Formatted date
 */
function formatDate($date, $format = 'd/m/Y') {
    if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
        return '';
    }
    
    return date($format, strtotime($date));
}

/**
 * Check if user is logged in
 * @return bool True if logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user is admin
 * @return bool True if admin, false otherwise
 */
function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
}

/**
 * Redirect to URL
 * @param string $url The URL to redirect to
 * @param int $code HTTP status code
 */
function redirect($url, $code = 302) {
    header("Location: $url", true, $code);
    exit;
}

/**
 * Get current URL
 * @return string Current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    
    return $protocol . '://' . $host . $uri;
}

/**
 * Send JSON response
 * @param mixed $data The data to send
 * @param int $code HTTP status code
 */
function jsonResponse($data, $code = 200) {
    http_response_code($code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}
