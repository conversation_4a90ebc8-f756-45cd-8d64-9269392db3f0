<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) : 'Concours Tunisie - Offres d\'Emploi'; ?></title>
    
    <!-- SEO Meta Tags -->
    <?php if (isset($seo_manager) && isset($seo_data)): ?>
        <?php $seo_manager->renderMetaTags($seo_data); ?>
    <?php else: ?>
        <meta name="description" content="Trouvez votre emploi idéal en Tunisie. Offres d'emploi CDI, CDD, stages dans tous les secteurs.">
        <meta name="keywords" content="emploi tunisie, travail tunisie, recrutement, offres emploi, jobs tunisia">
    <?php endif; ?>
    
    <!-- Stylesheets -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Additional CSS if provided -->
    <?php if (isset($additional_css)): ?>
        <?php echo $additional_css; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation Header -->
    <header class="main-header">
        <div class="container">
            <nav class="navbar">
                <div class="navbar-brand">
                    <a href="index.php">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="navbar-menu">
                    <ul class="navbar-nav">
                        <li><a href="/index.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">Accueil</a></li>
                        <li><a href="/jobs.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'jobs.php' ? 'active' : ''; ?>">Emplois</a></li>
                        <li><a href="/companies.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'companies.php' ? 'active' : ''; ?>">Entreprises</a></li>
                        <li><a href="/about.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'about.php' ? 'active' : ''; ?>">À propos</a></li>
                        <li><a href="/contact.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'contact.php' ? 'active' : ''; ?>">Contact</a></li>
                    </ul>
                </div>
                
                <div class="navbar-actions">
                    <?php if (session_status() === PHP_SESSION_ACTIVE && isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                        <?php if ($_SESSION['user_type'] === 'candidate'): ?>
                            <a href="candidate-dashboard.php" class="btn btn-outline">
                                <i class="fas fa-user"></i> Mon Espace
                            </a>
                        <?php elseif ($_SESSION['user_type'] === 'company'): ?>
                            <a href="company-dashboard.php" class="btn btn-outline">
                                <i class="fas fa-building"></i> Mon Espace
                            </a>
                        <?php endif; ?>
                        <a href="logout.php" class="btn btn-primary">
                            <i class="fas fa-sign-out-alt"></i> Déconnexion
                        </a>
                    <?php else: ?>
                        <div class="login-dropdown">
                            <button class="btn btn-outline dropdown-toggle">Connexion</button>
                            <div class="dropdown-menu">
                                <a href="login-candidate.php">Candidat</a>
                                <a href="login-company.php">Entreprise</a>
                            </div>
                        </div>
                        <a href="register-candidate.php" class="btn btn-primary">S'inscrire</a>
                    <?php endif; ?>
                </div>
                
                <!-- Mobile Menu Toggle -->
                <div class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </nav>
        </div>
    </header>
    
    <!-- Mobile Menu -->
    <div class="mobile-menu">
        <div class="mobile-menu-content">
            <ul>
                <li><a href="/index.php">Accueil</a></li>
                <li><a href="/jobs.php">Emplois</a></li>
                <li><a href="/companies.php">Entreprises</a></li>
                <li><a href="/about.php">À propos</a></li>
                <li><a href="/contact.php">Contact</a></li>
            </ul>
            
            <div class="mobile-actions">
                <?php if (session_status() === PHP_SESSION_ACTIVE && isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                    <?php if ($_SESSION['user_type'] === 'candidate'): ?>
                        <a href="candidate-dashboard.php" class="btn btn-outline">Mon Espace</a>
                    <?php elseif ($_SESSION['user_type'] === 'company'): ?>
                        <a href="company-dashboard.php" class="btn btn-outline">Mon Espace</a>
                    <?php endif; ?>
                    <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                <?php else: ?>
                    <a href="login-candidate.php" class="btn btn-outline">Connexion Candidat</a>
                    <a href="login-company.php" class="btn btn-outline">Connexion Entreprise</a>
                    <a href="register-candidate.php" class="btn btn-primary">S'inscrire</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Main Content Wrapper -->
    <main class="main-content">
