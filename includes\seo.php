<?php
/**
 * SEO Management System
 * Handles meta tags, structured data, and SEO optimization
 */

class SEOManager {
    private $site_name = "tunisieconcours.org Offres d'Emploi";
    private $site_url = "https://tunisieconcours.org";
    private $default_description = "Trouvez votre emploi idéal en Tunisie. Offres d'emploi CDI, CDD, stages dans tous les secteurs. Plateforme leader du recrutement en Tunisie.";
    private $default_keywords = "emploi tunisie, travail tunisie, recrutement, offres emploi, jobs tunisia, carrière tunisie, concours tunisie";
    private $default_image = "https://tunisieconcours.org/assets/images/logo-og.jpg";
    
    public function generateJobSEO($job, $company = null) {
        $title = $job['title'] . " - " . ($company['name'] ?? 'Emploi') . " | " . $this->site_name;
        $description = $this->generateJobDescription($job, $company);
        $keywords = $this->generateJobKeywords($job, $company);
        $canonical = $this->site_url . "/emploi/" . $job['slug'];
        
        return [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'canonical' => $canonical,
            'og_title' => $job['title'] . " - " . ($company['name'] ?? 'Emploi'),
            'og_description' => $description,
            'og_url' => $canonical,
            'og_image' => ($company && !empty($company['logo'])) ? "https://tunisieconcours.org/uploads/logos/" . $company['logo'] : $this->default_image,
            'structured_data' => $this->generateJobStructuredData($job, $company)
        ];
    }
    
    public function generatePageSEO($page_type, $data = []) {
        switch ($page_type) {
            case 'homepage':
                return [
                    'title' => $this->site_name . " - Emploi et Recrutement en Tunisie",
                    'description' => "Plateforme leader d'emploi en Tunisie. Découvrez des milliers d'offres d'emploi CDI, CDD, stages dans tous les secteurs. Recrutement simplifié pour candidats et entreprises.",
                    'keywords' => $this->default_keywords . ", plateforme emploi, recherche emploi tunisie",
                    'canonical' => $this->site_url . "/",
                    'og_title' => "Emploi Tunisie - Offres d'Emploi et Recrutement",
                    'og_description' => $this->default_description,
                    'og_url' => $this->site_url . "/",
                    'og_image' => $this->default_image
                ];
                
            case 'jobs_listing':
                $location = $data['location'] ?? '';
                $category = $data['category'] ?? '';
                $title_parts = ["Offres d'Emploi"];
                if ($category) $title_parts[] = $category;
                if ($location) $title_parts[] = $location;
                $title_parts[] = "Tunisie | " . $this->site_name;
                
                return [
                    'title' => implode(' - ', $title_parts),
                    'description' => "Découvrez les dernières offres d'emploi" . ($category ? " en $category" : "") . ($location ? " à $location" : "") . " en Tunisie. CDI, CDD, stages disponibles.",
                    'keywords' => $this->default_keywords . ($category ? ", emploi $category" : "") . ($location ? ", emploi $location" : ""),
                    'canonical' => $this->site_url . "/jobs.php" . ($data['query_string'] ? "?" . $data['query_string'] : ""),
                    'og_title' => "Offres d'Emploi" . ($category ? " $category" : "") . ($location ? " $location" : "") . " - Tunisie",
                    'og_description' => "Trouvez votre emploi idéal parmi nos offres" . ($category ? " en $category" : "") . ($location ? " à $location" : "") . " en Tunisie.",
                    'og_url' => $this->site_url . "/jobs.php",
                    'og_image' => $this->default_image
                ];
                
            case 'company_profile':
                $company = $data['company'];
                return [
                    'title' => $company['name'] . " - Profil Entreprise | " . $this->site_name,
                    'description' => "Découvrez " . $company['name'] . " et ses offres d'emploi en Tunisie. " . substr(strip_tags($company['description'] ?? ''), 0, 150) . "...",
                    'keywords' => $this->default_keywords . ", " . $company['name'] . ", entreprise tunisie",
                    'canonical' => $this->site_url . "/company/" . $company['id'],
                    'og_title' => $company['name'] . " - Recrutement Tunisie",
                    'og_description' => substr(strip_tags($company['description'] ?? ''), 0, 200),
                    'og_url' => $this->site_url . "/company/" . $company['id'],
                    'og_image' => $company['logo'] ? "https://tunisieconcours.org/uploads/logos/" . $company['logo'] : $this->default_image
                ];
                
            default:
                return $this->getDefaultSEO();
        }
    }
    
    private function generateJobDescription($job, $company) {
        $parts = [];
        $parts[] = "Offre d'emploi " . $job['title'];
        if ($company) $parts[] = "chez " . $company['name'];
        $parts[] = "à " . $job['location'];
        $parts[] = "en " . $this->formatJobType($job['job_type']);
        
        if ($job['salary_min'] && $job['salary_max']) {
            $parts[] = "Salaire: " . number_format($job['salary_min']) . "-" . number_format($job['salary_max']) . " " . $job['salary_currency'];
        }
        
        $description = implode(' ', $parts) . ". ";
        $description .= substr(strip_tags($job['description']), 0, 100) . "...";
        
        return $description;
    }
    
    private function generateJobKeywords($job, $company) {
        $keywords = [$this->default_keywords];
        $keywords[] = $job['title'];
        $keywords[] = "emploi " . $job['location'];
        $keywords[] = $this->formatJobType($job['job_type']);
        if ($company) $keywords[] = $company['name'];
        
        return implode(', ', $keywords);
    }
    
    private function formatJobType($job_type) {
        $types = [
            'cdi' => 'CDI',
            'cdd' => 'CDD', 
            'stage' => 'Stage',
            'freelance' => 'Freelance',
            'temps_partiel' => 'Temps partiel',
            'interim' => 'Intérim',
            'apprentissage' => 'Apprentissage',
            'saisonnier' => 'Saisonnier'
        ];
        return $types[$job_type] ?? $job_type;
    }
    
    private function generateJobStructuredData($job, $company) {
        $structured_data = [
            "@context" => "https://schema.org/",
            "@type" => "JobPosting",
            "title" => $job['title'],
            "description" => strip_tags($job['description']),
            "datePosted" => date('Y-m-d', strtotime($job['created_at'])),
            "validThrough" => $job['application_deadline'] ? date('Y-m-d', strtotime($job['application_deadline'])) : null,
            "employmentType" => strtoupper($job['job_type']),
            "jobLocation" => [
                "@type" => "Place",
                "address" => [
                    "@type" => "PostalAddress",
                    "addressLocality" => $job['location'],
                    "addressCountry" => "TN"
                ]
            ],
            "hiringOrganization" => [
                "@type" => "Organization",
                "name" => $company['name'] ?? "Entreprise",
                "sameAs" => $company['website'] ?? null
            ],
            "baseSalary" => null
        ];
        
        if ($job['salary_min'] && $job['salary_max']) {
            $structured_data['baseSalary'] = [
                "@type" => "MonetaryAmount",
                "currency" => $job['salary_currency'],
                "value" => [
                    "@type" => "QuantitativeValue",
                    "minValue" => $job['salary_min'],
                    "maxValue" => $job['salary_max'],
                    "unitText" => "MONTH"
                ]
            ];
        }
        
        return json_encode($structured_data, JSON_UNESCAPED_UNICODE);
    }
    
    public function renderMetaTags($seo_data) {
        echo '<title>' . htmlspecialchars($seo_data['title']) . '</title>' . "\n";
        echo '<meta name="description" content="' . htmlspecialchars($seo_data['description']) . '">' . "\n";
        echo '<meta name="keywords" content="' . htmlspecialchars($seo_data['keywords']) . '">' . "\n";
        echo '<link rel="canonical" href="' . htmlspecialchars($seo_data['canonical']) . '">' . "\n";
        
        // Open Graph tags
        echo '<meta property="og:title" content="' . htmlspecialchars($seo_data['og_title'] ?? $this->site_name) . '">' . "\n";
        echo '<meta property="og:description" content="' . htmlspecialchars($seo_data['og_description'] ?? $this->default_description) . '">' . "\n";
        echo '<meta property="og:url" content="' . htmlspecialchars($seo_data['og_url'] ?? $this->site_url) . '">' . "\n";
        echo '<meta property="og:image" content="' . htmlspecialchars($seo_data['og_image'] ?? $this->default_image) . '">' . "\n";
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:site_name" content="' . htmlspecialchars($this->site_name) . '">' . "\n";

        // Twitter Card tags
        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        echo '<meta name="twitter:title" content="' . htmlspecialchars($seo_data['og_title'] ?? $this->site_name) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . htmlspecialchars($seo_data['og_description'] ?? $this->default_description) . '">' . "\n";
        echo '<meta name="twitter:image" content="' . htmlspecialchars($seo_data['og_image'] ?? $this->default_image) . '">' . "\n";
        
        // Structured data
        if (isset($seo_data['structured_data'])) {
            echo '<script type="application/ld+json">' . "\n";
            echo $seo_data['structured_data'] . "\n";
            echo '</script>' . "\n";
        }
    }
    
    private function getDefaultSEO() {
        return [
            'title' => $this->site_name,
            'description' => $this->default_description,
            'keywords' => $this->default_keywords,
            'canonical' => $this->site_url,
            'og_title' => $this->site_name,
            'og_description' => $this->default_description,
            'og_url' => $this->site_url,
            'og_image' => $this->default_image
        ];
    }
}

// Initialize SEO Manager
$seo = new SEOManager();
?>
