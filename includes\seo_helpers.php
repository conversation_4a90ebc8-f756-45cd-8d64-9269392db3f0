<?php
/**
 * SEO Helper Functions
 * Utility functions for SEO optimization
 */

/**
 * Generate SEO-friendly slug from text
 */
function generateSEOSlug($title, $location = '', $company = '') {
    $slug_parts = [];
    
    // Clean and add title
    $title_slug = strtolower($title);
    $title_slug = str_replace(['é', 'è', 'ê', 'à', 'ç', 'ù', 'û', 'ô', 'î', 'â'], 
                             ['e', 'e', 'e', 'a', 'c', 'u', 'u', 'o', 'i', 'a'], $title_slug);
    $title_slug = preg_replace('/[^a-z0-9\s-]/', '', $title_slug);
    $title_slug = preg_replace('/\s+/', '-', trim($title_slug));
    $slug_parts[] = $title_slug;
    
    // Add location if provided
    if ($location) {
        $location_slug = strtolower($location);
        $location_slug = str_replace(['é', 'è', 'ê', 'à', 'ç', 'ù', 'û', 'ô', 'î', 'â'], 
                                   ['e', 'e', 'e', 'a', 'c', 'u', 'u', 'o', 'i', 'a'], $location_slug);
        $location_slug = preg_replace('/[^a-z0-9\s-]/', '', $location_slug);
        $location_slug = preg_replace('/\s+/', '-', trim($location_slug));
        $slug_parts[] = $location_slug;
    }
    
    $final_slug = implode('-', $slug_parts);
    
    // Ensure slug is not too long
    if (strlen($final_slug) > 100) {
        $final_slug = substr($final_slug, 0, 100);
        $final_slug = rtrim($final_slug, '-');
    }
    
    return $final_slug;
}

/**
 * Auto-generate meta description from job content
 */
function generateJobMetaDescription($job, $company = null) {
    $parts = [];
    
    // Start with job title and company
    $parts[] = "Offre d'emploi " . $job['title'];
    if ($company && !empty($company['name'])) {
        $parts[] = "chez " . $company['name'];
    }
    
    // Add location
    $parts[] = "à " . $job['location'];
    
    // Add contract type
    $job_types = [
        'cdi' => 'en CDI',
        'cdd' => 'en CDD',
        'stage' => 'stage',
        'freelance' => 'freelance',
        'temps_partiel' => 'temps partiel',
        'interim' => 'intérim',
        'apprentissage' => 'apprentissage',
        'saisonnier' => 'saisonnier'
    ];
    
    if (isset($job_types[$job['job_type']])) {
        $parts[] = $job_types[$job['job_type']];
    }
    
    // Add salary if available
    if (!empty($job['salary_min']) && !empty($job['salary_max'])) {
        $parts[] = "Salaire: " . number_format($job['salary_min']) . "-" . number_format($job['salary_max']) . " " . ($job['salary_currency'] ?? 'TND');
    }
    
    $description = implode(' ', $parts) . ". ";
    
    // Add excerpt from job description
    if (!empty($job['description'])) {
        $job_desc = strip_tags($job['description']);
        $job_desc = preg_replace('/\s+/', ' ', $job_desc);
        $excerpt = substr($job_desc, 0, 100);
        if (strlen($job_desc) > 100) {
            $excerpt .= "...";
        }
        $description .= $excerpt;
    }
    
    // Ensure description is not too long for SEO
    if (strlen($description) > 160) {
        $description = substr($description, 0, 157) . "...";
    }
    
    return $description;
}

/**
 * Generate keywords for job posting
 */
function generateJobKeywords($job, $company = null, $category = null) {
    $keywords = [];
    
    // Base keywords
    $keywords[] = "emploi tunisie";
    $keywords[] = "travail tunisie";
    $keywords[] = "recrutement";
    $keywords[] = "offres emploi";
    
    // Job-specific keywords
    $keywords[] = $job['title'];
    $keywords[] = "emploi " . $job['location'];
    
    // Contract type
    $job_types = [
        'cdi' => 'CDI',
        'cdd' => 'CDD',
        'stage' => 'stage',
        'freelance' => 'freelance',
        'temps_partiel' => 'temps partiel'
    ];
    
    if (isset($job_types[$job['job_type']])) {
        $keywords[] = $job_types[$job['job_type']];
        $keywords[] = "emploi " . $job_types[$job['job_type']];
    }
    
    // Company name
    if ($company && !empty($company['name'])) {
        $keywords[] = $company['name'];
        $keywords[] = "emploi " . $company['name'];
    }
    
    // Category
    if ($category && !empty($category['name'])) {
        $keywords[] = $category['name'];
        $keywords[] = "emploi " . $category['name'];
    }
    
    // Experience level
    if (!empty($job['experience_level'])) {
        $exp_levels = [
            'entry' => 'débutant',
            'mid' => 'expérimenté',
            'senior' => 'senior',
            'executive' => 'cadre'
        ];
        
        if (isset($exp_levels[$job['experience_level']])) {
            $keywords[] = $exp_levels[$job['experience_level']];
        }
    }
    
    return implode(', ', array_unique($keywords));
}

/**
 * Generate structured data for job posting
 */
function generateJobStructuredData($job, $company = null) {
    $structured_data = [
        "@context" => "https://schema.org/",
        "@type" => "JobPosting",
        "title" => $job['title'],
        "description" => strip_tags($job['description'] ?? ''),
        "datePosted" => date('Y-m-d', strtotime($job['created_at'])),
        "employmentType" => strtoupper($job['job_type']),
        "jobLocation" => [
            "@type" => "Place",
            "address" => [
                "@type" => "PostalAddress",
                "addressLocality" => $job['location'],
                "addressCountry" => "TN"
            ]
        ]
    ];
    
    // Add expiration date if available
    if (!empty($job['application_deadline'])) {
        $structured_data['validThrough'] = date('Y-m-d', strtotime($job['application_deadline']));
    }
    
    // Add company information
    if ($company) {
        $structured_data['hiringOrganization'] = [
            "@type" => "Organization",
            "name" => $company['name'] ?? "Entreprise"
        ];
        
        if (!empty($company['website'])) {
            $structured_data['hiringOrganization']['sameAs'] = $company['website'];
        }
        
        if (!empty($company['logo'])) {
            $structured_data['hiringOrganization']['logo'] = "https://tunisieconcours.org/uploads/logos/" . $company['logo'];
        }
    }
    
    // Add salary information
    if (!empty($job['salary_min']) && !empty($job['salary_max'])) {
        $structured_data['baseSalary'] = [
            "@type" => "MonetaryAmount",
            "currency" => $job['salary_currency'] ?? 'TND',
            "value" => [
                "@type" => "QuantitativeValue",
                "minValue" => $job['salary_min'],
                "maxValue" => $job['salary_max'],
                "unitText" => "MONTH"
            ]
        ];
    }
    
    // Add requirements if available
    if (!empty($job['requirements'])) {
        $structured_data['qualifications'] = strip_tags($job['requirements']);
    }
    
    // Add benefits if available
    if (!empty($job['benefits'])) {
        $structured_data['jobBenefits'] = strip_tags($job['benefits']);
    }
    
    return $structured_data;
}

/**
 * Generate Open Graph image URL for job
 */
function generateJobOGImage($job, $company = null) {
    // If company has logo, use it
    if ($company && !empty($company['logo'])) {
        return "https://tunisieconcours.org/uploads/logos/" . $company['logo'];
    }
    
    // Otherwise use default job image
    return "https://tunisieconcours.org/assets/images/default-job-og.jpg";
}

/**
 * Ensure slug is unique in database
 */
function ensureUniqueSlug($pdo, $base_slug, $job_id = null) {
    $slug = $base_slug;
    $counter = 1;
    
    while (true) {
        $query = "SELECT id FROM jobs WHERE slug = ?";
        $params = [$slug];
        
        // If updating existing job, exclude current job from check
        if ($job_id) {
            $query .= " AND id != ?";
            $params[] = $job_id;
        }
        
        $stmt = $pdo->prepare($query);
        $stmt->execute($params);
        
        if ($stmt->rowCount() == 0) {
            return $slug;
        }
        
        $slug = $base_slug . '-' . $counter;
        $counter++;
    }
}

/**
 * Update job SEO data when job is created/updated
 */
function updateJobSEO($pdo, $job_id) {
    // Get job with company and category info
    $stmt = $pdo->prepare("
        SELECT j.*, c.name as company_name, c.logo as company_logo, 
               cat.name as category_name
        FROM jobs j 
        LEFT JOIN companies c ON j.company_id = c.id 
        LEFT JOIN job_categories cat ON j.category_id = cat.id 
        WHERE j.id = ?
    ");
    $stmt->execute([$job_id]);
    $job = $stmt->fetch();
    
    if (!$job) return false;
    
    // Generate SEO-friendly slug if not exists
    if (empty($job['slug'])) {
        $base_slug = generateSEOSlug($job['title'], $job['location']);
        $unique_slug = ensureUniqueSlug($pdo, $base_slug, $job_id);
        
        // Update job with new slug
        $update_stmt = $pdo->prepare("UPDATE jobs SET slug = ? WHERE id = ?");
        $update_stmt->execute([$unique_slug, $job_id]);
        
        $job['slug'] = $unique_slug;
    }
    
    return true;
}
?>
