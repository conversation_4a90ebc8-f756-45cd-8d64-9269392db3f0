<?php
require_once 'config/database.php';
require_once 'includes/seo.php';

// Get job categories for search
$categories = [
    1 => 'Informatique et Technologies',
    2 => 'Finance et Comptabilité',
    3 => 'Marketing et Communication',
    4 => 'Ressources Humaines',
    5 => 'Vente et Commerce',
    6 => 'Ingénierie',
    7 => 'Santé et Médecine',
    8 => 'Éducation et Formation',
    9 => 'Droit et Juridique',
    10 => 'Design et Créativité',
    11 => 'Transport et Logistique',
    12 => 'Hôtellerie et Restauration',
    13 => 'Autres'
];

// Generate SEO data for homepage
$seo_data = $seo->generatePageSEO('homepage');
?>
<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php $seo->renderMetaTags($seo_data); ?>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link active">Accueil</a></li>
                        <li><a href="jobs.php" class="nav-link">Emplois</a></li>
                        <li><a href="companies.php" class="nav-link">Entreprises</a></li>
                        <li><a href="about.php" class="nav-link">À propos</a></li>
                        <li><a href="contact.php" class="nav-link">Contact</a></li>
                    </ul>

                    <div class="nav-actions">
                        <?php if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']): ?>
                            <?php if ($_SESSION['user_type'] === 'company'): ?>
                                <a href="company-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-building"></i> Tableau de bord
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php else: ?>
                                <a href="user-dashboard.php" class="btn btn-outline">
                                    <i class="fas fa-user"></i> Mon Profil
                                </a>
                                <a href="logout.php" class="btn btn-primary">Déconnexion</a>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="login-dropdown">
                                <button class="btn btn-outline dropdown-toggle">Connexion</button>
                                <div class="dropdown-menu">
                                    <a href="login-candidate.php">Candidat</a>
                                    <a href="login-company.php">Entreprise</a>
                                </div>
                            </div>
                            <a href="register-type.php" class="btn btn-primary">S'inscrire</a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-overlay"></div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Trouvez l'emploi de vos rêves en Tunisie</h1>
                <p class="hero-subtitle">La plateforme d'emploi leader en Tunisie - Découvrez des milliers d'opportunités des meilleures entreprises</p>
                
                <div class="job-search-form">
                    <form class="search-form" method="GET" action="jobs.php">
                        <div class="search-group">
                            <div class="search-field">
                                <i class="fas fa-search"></i>
                                <input type="text" name="keyword" placeholder="Titre du poste ou mots-clés">
                            </div>
                            
                            <div class="search-field">
                                <i class="fas fa-map-marker-alt"></i>
                                <select name="location">
                                    <option value="">Choisir le gouvernorat</option>
                                    <option value="tunis">Tunis</option>
                                    <option value="ariana">Ariana</option>
                                    <option value="ben_arous">Ben Arous</option>
                                    <option value="manouba">Manouba</option>
                                    <option value="nabeul">Nabeul</option>
                                    <option value="zaghouan">Zaghouan</option>
                                    <option value="bizerte">Bizerte</option>
                                    <option value="beja">Béja</option>
                                    <option value="jendouba">Jendouba</option>
                                    <option value="kef">Le Kef</option>
                                    <option value="siliana">Siliana</option>
                                    <option value="kairouan">Kairouan</option>
                                    <option value="kasserine">Kasserine</option>
                                    <option value="sidi_bouzid">Sidi Bouzid</option>
                                    <option value="sousse">Sousse</option>
                                    <option value="monastir">Monastir</option>
                                    <option value="mahdia">Mahdia</option>
                                    <option value="sfax">Sfax</option>
                                    <option value="gabes">Gabès</option>
                                    <option value="medenine">Médenine</option>
                                    <option value="tataouine">Tataouine</option>
                                    <option value="gafsa">Gafsa</option>
                                    <option value="tozeur">Tozeur</option>
                                    <option value="kebili">Kébili</option>
                                </select>
                            </div>

                            <div class="search-field">
                                <i class="fas fa-briefcase"></i>
                                <select name="category">
                                    <option value="">Toutes les catégories</option>
                                    <option value="informatique">Informatique & IT</option>
                                    <option value="finance">Finance & Comptabilité</option>
                                    <option value="marketing">Marketing & Communication</option>
                                    <option value="ingenierie">Ingénierie</option>
                                    <option value="sante">Santé & Médical</option>
                                    <option value="education">Éducation & Formation</option>
                                    <option value="vente">Vente & Commercial</option>
                                    <option value="rh">Ressources Humaines</option>
                                    <option value="juridique">Juridique</option>
                                    <option value="design">Design & Créatif</option>
                                    <option value="production">Production & Industrie</option>
                                    <option value="logistique">Logistique & Transport</option>
                                </select>
                            </div>

                            <div class="search-field">
                                <i class="fas fa-clock"></i>
                                <select name="job_type">
                                    <option value="">Type de contrat</option>
                                    <option value="cdi">CDI</option>
                                    <option value="cdd">CDD</option>
                                    <option value="stage">Stage</option>
                                    <option value="freelance">Freelance</option>
                                    <option value="temps_partiel">Temps Partiel</option>
                                    <option value="interim">Intérim</option>
                                    <option value="apprentissage">Apprentissage</option>
                                    <option value="saisonnier">Saisonnier</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-search">
                                <i class="fas fa-search"></i>
                                Rechercher
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">2,500+</span>
                        <span class="stat-label">Emplois disponibles</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">Entreprises</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">10,000+</span>
                        <span class="stat-label">Candidats</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Jobs Section -->
    <section id="jobs" class="featured-jobs">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Emplois en vedette</h2>
                <p class="section-subtitle">Découvrez les dernières opportunités d'emploi des meilleures entreprises en Tunisie</p>
            </div>
            
            <div class="jobs-grid">
                <?php
                // Get featured jobs from database
                $featured_jobs = $database->fetchAll("
                    SELECT j.*, c.name as company_name, c.logo as company_logo, c.governorate,
                           cat.name as category_name
                    FROM jobs j
                    JOIN companies c ON j.company_id = c.id
                    LEFT JOIN job_categories cat ON j.category_id = cat.id
                    WHERE j.is_active = 1 AND c.is_active = 1 AND j.is_featured = 1
                    ORDER BY j.created_at DESC
                    LIMIT 3
                ");

                // Tunisian governorates
                $governorates = [
                    'tunis' => 'Tunis', 'ariana' => 'Ariana', 'ben_arous' => 'Ben Arous', 'manouba' => 'Manouba',
                    'nabeul' => 'Nabeul', 'zaghouan' => 'Zaghouan', 'bizerte' => 'Bizerte', 'beja' => 'Béja',
                    'jendouba' => 'Jendouba', 'kef' => 'Le Kef', 'siliana' => 'Siliana', 'kairouan' => 'Kairouan',
                    'kasserine' => 'Kasserine', 'sidi_bouzid' => 'Sidi Bouzid', 'sousse' => 'Sousse',
                    'monastir' => 'Monastir', 'mahdia' => 'Mahdia', 'sfax' => 'Sfax', 'gabes' => 'Gabès',
                    'medenine' => 'Médenine', 'tataouine' => 'Tataouine', 'gafsa' => 'Gafsa',
                    'tozeur' => 'Tozeur', 'kebili' => 'Kébili'
                ];

                // Job types in French
                $job_types = [
                    'cdi' => 'CDI',
                    'cdd' => 'CDD',
                    'stage' => 'Stage',
                    'freelance' => 'Freelance',
                    'temps_partiel' => 'Temps Partiel',
                    'interim' => 'Intérim',
                    'apprentissage' => 'Apprentissage',
                    'saisonnier' => 'Saisonnier'
                ];

                foreach ($featured_jobs as $job):
                ?>
                <div class="job-card" onclick="window.location.href='emploi/<?php echo $job['slug'] ?? 'job-' . $job['id']; ?>'" style="cursor: pointer;">
                    <div class="job-header">
                        <div class="company-logo">
                            <?php if ($job['company_logo']): ?>
                                <img src="uploads/logos/<?php echo $job['company_logo']; ?>" alt="<?php echo htmlspecialchars($job['company_name']); ?>">
                            <?php else: ?>
                                <i class="fas fa-building" style="font-size: 2rem; color: #ccc;"></i>
                            <?php endif; ?>
                        </div>
                        <div class="job-info">
                            <h3 class="job-title"><?php echo htmlspecialchars($job['title']); ?></h3>
                            <p class="company-name"><?php echo htmlspecialchars($job['company_name']); ?></p>
                            <div class="job-meta">
                                <span class="location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo isset($governorates[$job['governorate']]) ? $governorates[$job['governorate']] : ($job['location'] ?? $job['governorate'] ?? 'Non spécifié'); ?>
                                </span>
                                <span class="job-type">
                                    <i class="fas fa-clock"></i>
                                    <?php echo $job_types[$job['job_type']] ?? $job['job_type']; ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="job-description">
                        <p><?php echo htmlspecialchars(substr($job['description'], 0, 100)) . '...'; ?></p>
                    </div>

                    <div class="job-tags">
                        <span class="tag"><?php echo htmlspecialchars($job['category_name']); ?></span>
                        <span class="tag"><?php echo htmlspecialchars($job['experience_level']); ?></span>
                        <?php if ($job['remote_work']): ?>
                            <span class="tag">Télétravail</span>
                        <?php endif; ?>
                    </div>

                    <div class="job-footer">
                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                            <span class="salary"><?php echo number_format($job['salary_min']) . ' - ' . number_format($job['salary_max']) . ' TND'; ?></span>
                        <?php else: ?>
                            <span class="salary">Salaire à négocier</span>
                        <?php endif; ?>
                        <a href="/emploi/<?php echo $job['slug'] ?? 'emploi-' . $job['id']; ?>" class="btn btn-apply" onclick="event.stopPropagation()">Postuler</a>
                    </div>
                </div>
                <?php endforeach; ?>

                <?php if (empty($featured_jobs)): ?>
                    <!-- Fallback content if no featured jobs -->
                    <div class="job-card">
                        <div class="job-header">
                            <div class="company-logo">
                                <i class="fas fa-building" style="font-size: 2rem; color: #ccc;"></i>
                            </div>
                            <div class="job-info">
                                <h3 class="job-title">Développeur Full Stack</h3>
                                <p class="company-name">Entreprise Technologique</p>
                                <div class="job-meta">
                                    <span class="location"><i class="fas fa-map-marker-alt"></i> Tunis</span>
                                    <span class="job-type"><i class="fas fa-clock"></i> CDI</span>
                                </div>
                            </div>
                        </div>

                        <div class="job-description">
                            <p>Nous recherchons un développeur web professionnel pour rejoindre notre équipe...</p>
                        </div>

                        <div class="job-tags">
                            <span class="tag">PHP</span>
                            <span class="tag">JavaScript</span>
                            <span class="tag">MySQL</span>
                        </div>

                        <div class="job-footer">
                            <span class="salary">1200 - 1800 TND</span>
                            <a href="jobs.php" class="btn btn-apply">Voir les emplois</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="section-footer">
                <a href="jobs.php" class="btn btn-secondary">Voir tous les emplois</a>
            </div>
        </div>
    </section>

    <!-- How it Works Section -->
    <section class="how-it-works">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Comment ça marche ?</h2>
                <p class="section-subtitle">Étapes simples pour trouver votre emploi idéal</p>
            </div>
            
            <div class="steps-grid">
                <div class="step-item">
                    <div class="step-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3 class="step-title">Créer un compte</h3>
                    <p class="step-description">Inscrivez-vous gratuitement et créez votre profil</p>
                </div>
                
                <div class="step-item">
                    <div class="step-icon">
                        <i class="fas fa-file-upload"></i>
                    </div>
                    <h3 class="step-title">Télécharger votre CV</h3>
                    <p class="step-description">Téléchargez votre CV et complétez vos informations professionnelles</p>
                </div>
                
                <div class="step-item">
                    <div class="step-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="step-title">Rechercher des emplois</h3>
                    <p class="step-description">Utilisez notre moteur de recherche pour trouver les emplois qui vous conviennent</p>
                </div>
                
                <div class="step-item">
                    <div class="step-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <h3 class="step-title">Postuler aux emplois</h3>
                    <p class="step-description">Postulez aux emplois qui correspondent à vos compétences et expériences</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <a href="index.php" class="logo">
                            <i class="fas fa-briefcase"></i>
                            <span>Concours Tunisie</span>
                        </a>
                        <p>La plateforme d'emploi leader en Tunisie</p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Liens rapides</h4>
                    <ul>
                        <li><a href="jobs.php">Emplois</a></li>
                        <li><a href="companies.php">Entreprises</a></li>
                        <li><a href="about.php">À propos</a></li>
                        <li><a href="contact.php">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Pour les candidats</h4>
                    <ul>
                        <li><a href="register-type.php">Créer un compte</a></li>
                        <li><a href="profile.php">Mon profil</a></li>
                        <li><a href="user-dashboard.php">Mes candidatures</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Pour les entreprises</h4>
                    <ul>
                        <li><a href="register-type.php">Inscription entreprise</a></li>
                        <li><a href="post-job.php">Publier une offre</a></li>
                        <li><a href="company-dashboard.php">Tableau de bord</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Concours Tunisie. Tous droits réservés.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
