<?php
/**
 * Direct Database Initialization Script
 * Run this if setup.php fails
 */

require_once 'config/database.php';

try {
    echo "Connecting to database...\n";
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Connected successfully!\n\n";

    // Create tables one by one
    echo "Creating tables...\n";

    // Users table
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
        last_name VA<PERSON>HA<PERSON>(50) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        date_of_birth DATE,
        gender ENUM('male', 'female') DEFAULT 'male',
        address TEXT,
        city VARCHAR(50),
        governorate VARCHAR(50),
        profile_image VARCHAR(255),
        cv_file VARCHAR(255),
        skills TEXT,
        experience_years INT DEFAULT 0,
        education_level ENUM('high_school', 'bachelor', 'master', 'phd', 'other') DEFAULT 'bachelor',
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✓ Users table created\n";

    // Companies table
    $sql = "CREATE TABLE IF NOT EXISTS companies (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        website VARCHAR(255),
        logo VARCHAR(255),
        description TEXT,
        industry VARCHAR(100),
        company_size ENUM('1-10', '11-50', '51-200', '201-500', '500+') DEFAULT '1-10',
        address TEXT,
        city VARCHAR(50),
        governorate VARCHAR(50),
        founded_year YEAR,
        is_verified BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✓ Companies table created\n";

    // Job categories table
    $sql = "CREATE TABLE IF NOT EXISTS job_categories (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        name_ar VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✓ Job categories table created\n";

    // Jobs table
    $sql = "CREATE TABLE IF NOT EXISTS jobs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        company_id INT NOT NULL,
        category_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT NOT NULL,
        requirements TEXT,
        benefits TEXT,
        salary_min DECIMAL(10,2),
        salary_max DECIMAL(10,2),
        salary_currency VARCHAR(3) DEFAULT 'TND',
        job_type ENUM('cdi', 'cdd', 'stage', 'freelance', 'temps_partiel', 'interim', 'apprentissage', 'saisonnier', 'full_time', 'part_time', 'contract', 'internship') DEFAULT 'cdi',
        experience_level ENUM('entry', 'mid', 'senior', 'executive') DEFAULT 'entry',
        education_level ENUM('high_school', 'bachelor', 'master', 'phd', 'other') DEFAULT 'bachelor',
        location VARCHAR(100),
        governorate VARCHAR(50),
        remote_work BOOLEAN DEFAULT FALSE,
        application_deadline DATE,
        is_featured BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        views_count INT DEFAULT 0,
        applications_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES job_categories(id)
    )";
    $pdo->exec($sql);
    echo "✓ Jobs table created\n";

    // Job skills table
    $sql = "CREATE TABLE IF NOT EXISTS job_skills (
        id INT PRIMARY KEY AUTO_INCREMENT,
        job_id INT NOT NULL,
        skill_name VARCHAR(100) NOT NULL,
        is_required BOOLEAN DEFAULT TRUE,
        FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "✓ Job skills table created\n";

    // Job applications table
    $sql = "CREATE TABLE IF NOT EXISTS job_applications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        job_id INT NOT NULL,
        user_id INT NOT NULL,
        cover_letter TEXT,
        cv_file VARCHAR(255),
        status ENUM('pending', 'reviewed', 'shortlisted', 'rejected', 'hired') DEFAULT 'pending',
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        reviewed_at TIMESTAMP NULL,
        notes TEXT,
        FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_application (job_id, user_id)
    )";
    $pdo->exec($sql);
    echo "✓ Job applications table created\n";

    // Admin users table
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "✓ Admin users table created\n";

    // Insert job categories
    echo "\nInserting job categories...\n";
    $categories = [
        ['Informatique & IT', 'تكنولوجيا المعلومات', 'informatique', 'Développement logiciel, développement web, administration système', 'fas fa-laptop-code'],
        ['Finance & Comptabilité', 'المالية والمحاسبة', 'finance', 'Comptabilité, banque, analyse financière', 'fas fa-calculator'],
        ['Marketing & Communication', 'التسويق والاتصال', 'marketing', 'Marketing digital, vente, publicité', 'fas fa-bullhorn'],
        ['Ingénierie', 'الهندسة', 'ingenierie', 'Génie civil, mécanique, électrique', 'fas fa-cogs'],
        ['Santé & Médical', 'الصحة والطب', 'sante', 'Médical, soins infirmiers, pharmaceutique', 'fas fa-heartbeat'],
        ['Éducation & Formation', 'التعليم والتدريب', 'education', 'Enseignement, formation, recherche académique', 'fas fa-graduation-cap'],
        ['Ressources Humaines', 'الموارد البشرية', 'rh', 'Gestion RH, recrutement, formation', 'fas fa-users'],
        ['Service Client', 'خدمة العملاء', 'service-client', 'Support, centre d\'appels, relations clients', 'fas fa-headset'],
        ['Juridique', 'القانون', 'juridique', 'Conseil juridique, conformité, contrats', 'fas fa-gavel'],
        ['Design & Créatif', 'التصميم والإبداع', 'design', 'Design graphique, UI/UX, arts créatifs', 'fas fa-palette'],
        ['Vente & Commercial', 'المبيعات والتجارة', 'vente', 'Vente, commerce, développement commercial', 'fas fa-handshake'],
        ['Production & Industrie', 'الإنتاج والصناعة', 'production', 'Production, industrie, qualité', 'fas fa-industry'],
        ['Logistique & Transport', 'اللوجستيات والنقل', 'logistique', 'Logistique, transport, supply chain', 'fas fa-truck']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO job_categories (name, name_ar, slug, description, icon) VALUES (?, ?, ?, ?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
    }
    echo "✓ Job categories inserted\n";

    // Insert admin user
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['admin', '<EMAIL>', $adminPassword, 'System Administrator', 'super_admin']);
    echo "✓ Admin user created (username: admin, password: admin123)\n";

    // Create directories
    echo "\nCreating directories...\n";
    $directories = ['uploads', 'uploads/cvs', 'uploads/logos', 'uploads/profiles', 'logs'];
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✓ Created directory: $dir\n";
        }
    }

    // Create .htaccess for uploads security
    $htaccess_content = "Options -Indexes\n";
    $htaccess_content .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
    $htaccess_content .= "    deny from all\n";
    $htaccess_content .= "</Files>\n";
    file_put_contents('uploads/.htaccess', $htaccess_content);
    echo "✓ Security .htaccess created\n";

    echo "\n🎉 Database initialization completed successfully!\n";
    echo "You can now visit: http://localhost:8000/\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
