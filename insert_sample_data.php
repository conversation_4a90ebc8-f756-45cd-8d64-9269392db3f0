<?php
require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Inserting sample companies...\n";
    
    // Insert sample companies
    $stmt = $pdo->prepare("INSERT IGNORE INTO companies (name, email, password, phone, website, description, industry, company_size, address, city, governorate, founded_year, is_verified, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $companies = [
        ['شركة التكنولوجيا المتقدمة', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.techadvanced.tn', 'شركة رائدة في مجال تطوير البرمجيات والحلول التقنية', 'Technology', '51-200', 'Avenue Habib Bourguiba, Tunis', 'Tunis', 'tunis', 2010, 1, 1],
        ['مكتب المحاسبة الدولي', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.accounting-intl.tn', 'مكتب محاسبة معتمد يقدم خدمات المحاسبة والاستشارات المالية', 'Finance', '11-50', 'Avenue Farhat Hached, Sfax', 'Sfax', 'sfax', 2005, 1, 1],
        ['وكالة الإبداع للتسويق', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.creative-marketing.tn', 'وكالة تسويق رقمي متخصصة في الحملات الإعلانية والتسويق الإلكتروني', 'Marketing', '11-50', 'Avenue de la République, Sousse', 'Sousse', 'sousse', 2015, 1, 1],
        ['مجموعة الهندسة الحديثة', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '********', 'www.modern-eng.tn', 'مجموعة هندسية متخصصة في المشاريع الكبرى والاستشارات الهندسية', 'Engineering', '201-500', 'Zone Industrielle, Ariana', 'Ariana', 'ariana', 2000, 1, 1],
        ['عيادة النور الطبية', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '72345678', 'www.nour-clinic.tn', 'عيادة طبية متعددة التخصصات تقدم خدمات صحية شاملة', 'Healthcare', '51-200', 'Avenue de l\'Indépendance, Nabeul', 'Nabeul', 'nabeul', 2012, 1, 1]
    ];
    
    foreach ($companies as $company) {
        $stmt->execute($company);
    }
    echo "✓ Companies inserted\n";

    echo "Inserting sample jobs...\n";
    
    // Insert sample jobs
    $stmt = $pdo->prepare("INSERT IGNORE INTO jobs (company_id, category_id, title, description, requirements, benefits, salary_min, salary_max, salary_currency, job_type, experience_level, education_level, location, governorate, remote_work, application_deadline, is_featured, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $jobs = [
        [1, 1, 'مطور ويب Full Stack', 'نبحث عن مطور ويب محترف للانضمام إلى فريقنا المتميز. المرشح المثالي يجب أن يكون لديه خبرة في تطوير التطبيقات الويب باستخدام التقنيات الحديثة.', 'خبرة لا تقل عن 3 سنوات في تطوير الويب، إتقان PHP, JavaScript, HTML, CSS، خبرة في MySQL وقواعد البيانات', 'راتب تنافسي، تأمين صحي شامل، بيئة عمل مريحة، فرص التطوير المهني', 1200.00, 1800.00, 'TND', 'cdi', 'mid', 'bachelor', 'Tunis', 'tunis', 0, '2024-12-31', 1, 1],
        
        [2, 2, 'محاسب أول', 'مطلوب محاسب خبير للعمل في مكتب محاسبة رائد. المرشح المناسب يجب أن يكون لديه خبرة واسعة في المحاسبة والتدقيق.', 'شهادة في المحاسبة أو المالية، خبرة لا تقل عن 5 سنوات، إتقان برامج المحاسبة، معرفة بالقوانين المالية التونسية', 'راتب مجزي، بدلات إضافية، تأمين صحي، إجازات مدفوعة الأجر', 1000.00, 1400.00, 'TND', 'cdi', 'senior', 'bachelor', 'Sfax', 'sfax', 0, '2024-12-31', 1, 1],
        
        [3, 3, 'مدير تسويق رقمي', 'نبحث عن مدير تسويق رقمي مبدع وخبير لقيادة استراتيجيات التسويق الإلكتروني لعملائنا.', 'خبرة في التسويق الرقمي لا تقل عن 4 سنوات، إتقان Google Ads و Facebook Ads، معرفة بأدوات التحليل، مهارات قيادية وإبداعية', 'راتب ممتاز، عمولات على النتائج، تدريب مستمر، بيئة عمل إبداعية', 1300.00, 1700.00, 'TND', 'cdi', 'mid', 'bachelor', 'Sousse', 'sousse', 1, '2024-12-31', 1, 1],
        
        [4, 4, 'مهندس مدني', 'مطلوب مهندس مدني للعمل في مشاريع البناء والتطوير العقاري.', 'شهادة هندسة مدنية، خبرة لا تقل عن 2 سنة، معرفة ببرامج التصميم الهندسي، القدرة على العمل تحت الضغط', 'راتب تنافسي، تأمين شامل، سيارة خدمة، فرص الترقي', 1100.00, 1500.00, 'TND', 'cdi', 'entry', 'bachelor', 'Ariana', 'ariana', 0, '2024-12-31', 0, 1],
        
        [5, 5, 'ممرض/ممرضة', 'مطلوب ممرض أو ممرضة للعمل في عيادة طبية متخصصة.', 'شهادة في التمريض، ترخيص مزاولة المهنة، خبرة سابقة مفضلة، مهارات تواصل جيدة', 'راتب مناسب، تأمين صحي، تدريب مستمر، بيئة عمل مهنية', 900.00, 1200.00, 'TND', 'cdd', 'entry', 'bachelor', 'Nabeul', 'nabeul', 0, '2024-12-31', 0, 1],
        
        [1, 1, 'مطور تطبيقات الجوال', 'نبحث عن مطور تطبيقات جوال ماهر للعمل على مشاريع مبتكرة.', 'خبرة في تطوير التطبيقات، إتقان React Native أو Flutter، معرفة بـ API Integration، خبرة في النشر على المتاجر', 'راتب ممتاز، مشاريع متنوعة، تطوير مهني، فريق عمل متميز', 1400.00, 1900.00, 'TND', 'freelance', 'mid', 'bachelor', 'Tunis', 'tunis', 1, '2024-12-31', 1, 1],
        
        [2, 2, 'مساعد محاسب', 'فرصة ممتازة للخريجين الجدد للبدء في مجال المحاسبة.', 'شهادة في المحاسبة أو ما يعادلها، معرفة أساسية ببرامج المحاسبة، دقة في العمل والالتزام بالمواعيد', 'فرصة للتعلم، تدريب شامل، راتب مناسب للمبتدئين، إمكانية الترقي', 700.00, 900.00, 'TND', 'stage', 'entry', 'bachelor', 'Sfax', 'sfax', 0, '2024-12-31', 0, 1],
        
        [3, 3, 'أخصائي وسائل التواصل الاجتماعي', 'مطلوب أخصائي لإدارة حسابات وسائل التواصل الاجتماعي لعملائنا.', 'خبرة في إدارة وسائل التواصل، مهارات إبداعية في المحتوى، معرفة بأدوات التصميم، مهارات كتابة ممتازة', 'بيئة عمل إبداعية، مرونة في العمل، فرص التطوير، راتب تنافسي', 800.00, 1100.00, 'TND', 'temps_partiel', 'entry', 'bachelor', 'Sousse', 'sousse', 1, '2024-12-31', 0, 1]
    ];
    
    foreach ($jobs as $job) {
        $stmt->execute($job);
    }
    echo "✓ Jobs inserted\n";

    // Insert job skills
    echo "Inserting job skills...\n";
    $stmt = $pdo->prepare("INSERT IGNORE INTO job_skills (job_id, skill_name, is_required) VALUES (?, ?, ?)");
    
    $skills = [
        [1, 'PHP', 1], [1, 'JavaScript', 1], [1, 'MySQL', 1], [1, 'HTML/CSS', 1],
        [2, 'Comptabilité', 1], [2, 'Excel', 1], [2, 'SAP', 0],
        [3, 'Marketing Digital', 1], [3, 'Google Ads', 1], [3, 'Facebook Ads', 1],
        [4, 'AutoCAD', 1], [4, 'Génie Civil', 1],
        [5, 'Soins Infirmiers', 1], [5, 'Soins aux Patients', 1],
        [6, 'React Native', 1], [6, 'Flutter', 0], [6, 'API Integration', 1],
        [7, 'Comptabilité', 1], [7, 'Excel', 1],
        [8, 'Gestion de Contenu', 1], [8, 'Design', 0], [8, 'Rédaction Créative', 1]
    ];
    
    foreach ($skills as $skill) {
        $stmt->execute($skill);
    }
    echo "✓ Job skills inserted\n";

    // Update job counts
    $pdo->exec("UPDATE jobs SET views_count = FLOOR(RAND() * 500) + 50");
    $pdo->exec("UPDATE jobs SET applications_count = FLOOR(RAND() * 20) + 1");
    echo "✓ Job counts updated\n";

    echo "\n🎉 Sample data inserted successfully!\n";
    echo "You can now visit: http://localhost:8000/\n";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
