<?php
/**
 * COMPLETE DATABASE INSTALLATION SCRIPT
 * Creates all tables with all columns for tunisieconcours.org
 */

echo "🗄️ COMPLETE DATABASE INSTALLATION\n";
echo "==================================\n";
echo "Database: tunisieconcours_dirr\n";
echo "User: tunisieconcours_dirr\n";
echo "Password: &w],o=IuJmAS.ar~\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n\n";

// Database connection
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=tunisieconcours_dirr;charset=utf8mb4",
        "tunisieconcours_dirr",
        "&w],o=IuJmAS.ar~",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "✅ Database connection successful\n\n";
} catch (PDOException $e) {
    die("❌ Database connection failed: " . $e->getMessage() . "\n");
}

// Drop all existing tables
echo "🗑️ CLEANING EXISTING TABLES\n";
echo "============================\n";

$pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
$tables_to_drop = [
    'job_applications', 'saved_jobs', 'jobs', 'companies', 
    'users', 'job_categories', 'admin_users', 'governorates'
];

foreach ($tables_to_drop as $table) {
    try {
        $pdo->exec("DROP TABLE IF EXISTS `$table`");
        echo "✅ Dropped table: $table\n";
    } catch (Exception $e) {
        echo "⚠️ Table $table: " . $e->getMessage() . "\n";
    }
}
$pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

echo "\n📊 CREATING ALL TABLES\n";
echo "======================\n";

// 1. Governorates table
$pdo->exec("
    CREATE TABLE `governorates` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `name_ar` varchar(100) DEFAULT NULL,
        `code` varchar(10) NOT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: governorates\n";

// 2. Admin users table
$pdo->exec("
    CREATE TABLE `admin_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
        `avatar` varchar(255) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `last_login` timestamp NULL DEFAULT NULL,
        `login_attempts` int(11) DEFAULT 0,
        `locked_until` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: admin_users\n";

// 3. Job categories table
$pdo->exec("
    CREATE TABLE `job_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `name_ar` varchar(100) DEFAULT NULL,
        `slug` varchar(255) UNIQUE,
        `description` text,
        `description_ar` text,
        `icon` varchar(50) DEFAULT NULL,
        `color` varchar(7) DEFAULT '#007bff',
        `image` varchar(255) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `sort_order` int(11) DEFAULT 0,
        `jobs_count` int(11) DEFAULT 0,
        `meta_title` varchar(255) DEFAULT NULL,
        `meta_description` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `name` (`name`),
        KEY `is_active` (`is_active`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: job_categories\n";

// 4. Companies table
$pdo->exec("
    CREATE TABLE `companies` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `slug` varchar(255) UNIQUE,
        `email` varchar(255) NOT NULL,
        `password` varchar(255) NOT NULL,
        `description` text,
        `description_ar` text,
        `industry` varchar(100) DEFAULT NULL,
        `website` varchar(255) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `fax` varchar(20) DEFAULT NULL,
        `address` text,
        `city` varchar(100) DEFAULT NULL,
        `governorate` varchar(100) DEFAULT NULL,
        `postal_code` varchar(10) DEFAULT NULL,
        `country` varchar(100) DEFAULT 'Tunisia',
        `logo` varchar(255) DEFAULT NULL,
        `cover_image` varchar(255) DEFAULT NULL,
        `employee_count` enum('1-10','11-50','51-200','201-500','501-1000','1000+') DEFAULT NULL,
        `founded_year` int(4) DEFAULT NULL,
        `linkedin_url` varchar(255) DEFAULT NULL,
        `facebook_url` varchar(255) DEFAULT NULL,
        `twitter_url` varchar(255) DEFAULT NULL,
        `instagram_url` varchar(255) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `is_verified` tinyint(1) DEFAULT 0,
        `is_premium` tinyint(1) DEFAULT 0,
        `verification_date` timestamp NULL DEFAULT NULL,
        `jobs_count` int(11) DEFAULT 0,
        `views_count` int(11) DEFAULT 0,
        `email_verified` tinyint(1) DEFAULT 0,
        `email_verification_token` varchar(255) DEFAULT NULL,
        `password_reset_token` varchar(255) DEFAULT NULL,
        `password_reset_expires` timestamp NULL DEFAULT NULL,
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `email` (`email`),
        UNIQUE KEY `slug` (`slug`),
        KEY `is_active` (`is_active`),
        KEY `is_verified` (`is_verified`),
        KEY `governorate` (`governorate`),
        KEY `industry` (`industry`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: companies\n";

// 5. Users table
$pdo->exec("
    CREATE TABLE `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `email` varchar(255) NOT NULL,
        `password` varchar(255) NOT NULL,
        `first_name` varchar(100) NOT NULL,
        `last_name` varchar(100) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `date_of_birth` date DEFAULT NULL,
        `gender` enum('male','female','other') DEFAULT NULL,
        `nationality` varchar(100) DEFAULT 'Tunisian',
        `address` text,
        `city` varchar(100) DEFAULT NULL,
        `governorate` varchar(100) DEFAULT NULL,
        `postal_code` varchar(10) DEFAULT NULL,
        `country` varchar(100) DEFAULT 'Tunisia',
        `avatar` varchar(255) DEFAULT NULL,
        `education_level` enum('high_school','bachelor','master','phd','other') DEFAULT NULL,
        `field_of_study` varchar(255) DEFAULT NULL,
        `university` varchar(255) DEFAULT NULL,
        `graduation_year` int(4) DEFAULT NULL,
        `experience_years` int(11) DEFAULT NULL,
        `current_position` varchar(255) DEFAULT NULL,
        `current_company` varchar(255) DEFAULT NULL,
        `skills` text,
        `languages` text,
        `cv_file` varchar(255) DEFAULT NULL,
        `portfolio_url` varchar(255) DEFAULT NULL,
        `linkedin_url` varchar(255) DEFAULT NULL,
        `github_url` varchar(255) DEFAULT NULL,
        `expected_salary_min` decimal(10,2) DEFAULT NULL,
        `expected_salary_max` decimal(10,2) DEFAULT NULL,
        `availability` enum('immediate','1_week','2_weeks','1_month','3_months','other') DEFAULT 'immediate',
        `job_preferences` text,
        `is_active` tinyint(1) DEFAULT 1,
        `is_public` tinyint(1) DEFAULT 1,
        `email_verified` tinyint(1) DEFAULT 0,
        `email_verification_token` varchar(255) DEFAULT NULL,
        `password_reset_token` varchar(255) DEFAULT NULL,
        `password_reset_expires` timestamp NULL DEFAULT NULL,
        `last_login` timestamp NULL DEFAULT NULL,
        `profile_views` int(11) DEFAULT 0,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `email` (`email`),
        KEY `is_active` (`is_active`),
        KEY `governorate` (`governorate`),
        KEY `education_level` (`education_level`),
        KEY `experience_years` (`experience_years`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: users\n";

// 6. Jobs table
$pdo->exec("
    CREATE TABLE `jobs` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `company_id` int(11) NOT NULL,
        `category_id` int(11) NOT NULL,
        `title` varchar(255) NOT NULL,
        `title_ar` varchar(255) DEFAULT NULL,
        `slug` varchar(255) UNIQUE,
        `description` text NOT NULL,
        `description_ar` text,
        `requirements` text,
        `requirements_ar` text,
        `benefits` text,
        `benefits_ar` text,
        `responsibilities` text,
        `responsibilities_ar` text,
        `job_type` enum('cdi','cdd','stage','freelance','temps_partiel','interim','apprentissage','saisonnier') NOT NULL,
        `experience_level` enum('entry','mid','senior','executive') DEFAULT NULL,
        `education_level` enum('high_school','bachelor','master','phd','other') DEFAULT NULL,
        `location` varchar(100) NOT NULL,
        `remote_work` tinyint(1) DEFAULT 0,
        `salary_min` decimal(10,2) DEFAULT NULL,
        `salary_max` decimal(10,2) DEFAULT NULL,
        `salary_currency` varchar(3) DEFAULT 'TND',
        `salary_period` enum('hour','day','month','year') DEFAULT 'month',
        `salary_negotiable` tinyint(1) DEFAULT 0,
        `application_deadline` date DEFAULT NULL,
        `positions_available` int(11) DEFAULT 1,
        `contact_email` varchar(255) DEFAULT NULL,
        `contact_phone` varchar(20) DEFAULT NULL,
        `application_instructions` text,
        `required_documents` text,
        `is_active` tinyint(1) DEFAULT 1,
        `is_featured` tinyint(1) DEFAULT 0,
        `is_urgent` tinyint(1) DEFAULT 0,
        `is_premium` tinyint(1) DEFAULT 0,
        `views_count` int(11) DEFAULT 0,
        `applications_count` int(11) DEFAULT 0,
        `saves_count` int(11) DEFAULT 0,
        `shares_count` int(11) DEFAULT 0,
        `status` enum('draft','published','paused','closed','expired') DEFAULT 'published',
        `rejection_reason` text DEFAULT NULL,
        `admin_notes` text DEFAULT NULL,
        `seo_title` varchar(255) DEFAULT NULL,
        `seo_description` text DEFAULT NULL,
        `seo_keywords` text DEFAULT NULL,
        `published_at` timestamp NULL DEFAULT NULL,
        `expires_at` timestamp NULL DEFAULT NULL,
        `featured_until` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `company_id` (`company_id`),
        KEY `category_id` (`category_id`),
        KEY `location` (`location`),
        KEY `job_type` (`job_type`),
        KEY `is_active` (`is_active`),
        KEY `is_featured` (`is_featured`),
        KEY `published_at` (`published_at`),
        KEY `expires_at` (`expires_at`),
        CONSTRAINT `jobs_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
        CONSTRAINT `jobs_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: jobs\n";

// 7. Job applications table
$pdo->exec("
    CREATE TABLE `job_applications` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `job_id` int(11) NOT NULL,
        `user_id` int(11) NOT NULL,
        `cover_letter` text NOT NULL,
        `cv_file` varchar(255) NOT NULL,
        `additional_documents` text DEFAULT NULL,
        `status` enum('pending','reviewed','shortlisted','interviewed','hired','rejected','withdrawn') DEFAULT 'pending',
        `notes` text,
        `company_notes` text,
        `admin_notes` text,
        `interview_date` timestamp NULL DEFAULT NULL,
        `interview_location` varchar(255) DEFAULT NULL,
        `interview_type` enum('in_person','phone','video','other') DEFAULT NULL,
        `interview_notes` text,
        `salary_offered` decimal(10,2) DEFAULT NULL,
        `start_date_offered` date DEFAULT NULL,
        `response_date` timestamp NULL DEFAULT NULL,
        `rejection_reason` text DEFAULT NULL,
        `feedback` text DEFAULT NULL,
        `rating` int(1) DEFAULT NULL,
        `is_read_by_company` tinyint(1) DEFAULT 0,
        `is_read_by_user` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_application` (`job_id`,`user_id`),
        KEY `user_id` (`user_id`),
        KEY `status` (`status`),
        KEY `created_at` (`created_at`),
        CONSTRAINT `job_applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
        CONSTRAINT `job_applications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: job_applications\n";

// 8. Saved jobs table
$pdo->exec("
    CREATE TABLE `saved_jobs` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `job_id` int(11) NOT NULL,
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_save` (`user_id`,`job_id`),
        KEY `user_id` (`user_id`),
        KEY `job_id` (`job_id`),
        KEY `created_at` (`created_at`),
        CONSTRAINT `saved_jobs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `saved_jobs_ibfk_2` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");
echo "✅ Created table: saved_jobs\n";

echo "\n📊 INSERTING SAMPLE DATA\n";
echo "========================\n";

// Insert governorates
$pdo->exec("
    INSERT INTO `governorates` (`name`, `name_ar`, `code`) VALUES
    ('Tunis', 'تونس', 'TN'),
    ('Ariana', 'أريانة', 'AR'),
    ('Ben Arous', 'بن عروس', 'BA'),
    ('Manouba', 'منوبة', 'MN'),
    ('Nabeul', 'نابل', 'NB'),
    ('Zaghouan', 'زغوان', 'ZG'),
    ('Bizerte', 'بنزرت', 'BZ'),
    ('Béja', 'باجة', 'BJ'),
    ('Jendouba', 'جندوبة', 'JE'),
    ('Kef', 'الكاف', 'KF'),
    ('Siliana', 'سليانة', 'SL'),
    ('Kairouan', 'القيروان', 'KR'),
    ('Kasserine', 'القصرين', 'KS'),
    ('Sidi Bouzid', 'سيدي بوزيد', 'SB'),
    ('Sousse', 'سوسة', 'SS'),
    ('Monastir', 'المنستير', 'MS'),
    ('Mahdia', 'المهدية', 'MH'),
    ('Sfax', 'صفاقس', 'SF'),
    ('Gafsa', 'قفصة', 'GF'),
    ('Tozeur', 'توزر', 'TZ'),
    ('Kebili', 'قبلي', 'KB'),
    ('Gabes', 'قابس', 'GB'),
    ('Medenine', 'مدنين', 'MD'),
    ('Tataouine', 'تطاوين', 'TT')
");
echo "✅ Inserted governorates (24 governorates)\n";

// Insert admin user
$admin_password = password_hash('admin123', PASSWORD_DEFAULT);
$pdo->exec("
    INSERT INTO `admin_users` (`username`, `email`, `password`, `full_name`, `role`) VALUES
    ('admin', '<EMAIL>', '$admin_password', 'Administrateur Principal', 'super_admin')
");
echo "✅ Admin user created (admin/admin123)\n";

// Insert job categories
$pdo->exec("
    INSERT INTO `job_categories` (`name`, `slug`, `description`, `icon`, `color`) VALUES
    ('Informatique et Technologies', 'informatique-et-technologies', 'Développement, programmation, IT, systèmes', 'fas fa-laptop-code', '#007bff'),
    ('Finance et Comptabilité', 'finance-comptabilite', 'Comptabilité, finance, audit, banque', 'fas fa-calculator', '#28a745'),
    ('Marketing et Communication', 'marketing-communication', 'Marketing digital, communication, publicité', 'fas fa-bullhorn', '#dc3545'),
    ('Ressources Humaines', 'ressources-humaines', 'RH, recrutement, formation', 'fas fa-users', '#ffc107'),
    ('Vente et Commerce', 'vente-commerce', 'Vente, commerce, relation client', 'fas fa-handshake', '#17a2b8'),
    ('Ingénierie', 'ingenierie', 'Ingénierie civile, mécanique, électrique', 'fas fa-cogs', '#6f42c1'),
    ('Santé et Médical', 'sante-medical', 'Médecine, pharmacie, soins', 'fas fa-heartbeat', '#e83e8c'),
    ('Éducation et Formation', 'education-formation', 'Enseignement, formation, éducation', 'fas fa-graduation-cap', '#fd7e14'),
    ('Juridique', 'juridique', 'Droit, juridique, notariat', 'fas fa-gavel', '#6c757d'),
    ('Transport et Logistique', 'transport-logistique', 'Transport, logistique, supply chain', 'fas fa-truck', '#20c997')
");
echo "✅ Job categories created (10 categories)\n";

// Insert sample companies
$company_password = password_hash('company123', PASSWORD_DEFAULT);
$pdo->exec("
    INSERT INTO `companies` (`name`, `slug`, `email`, `password`, `description`, `industry`, `website`, `phone`, `city`, `governorate`, `employee_count`, `founded_year`, `is_verified`) VALUES
    ('TechnoSoft Tunisia', 'technosoft-tunisia', '<EMAIL>', '$company_password', 'Société spécialisée dans le développement de solutions logicielles innovantes pour les entreprises tunisiennes et internationales.', 'Informatique', 'https://technosoft.tn', '+216 71 123 456', 'Tunis', 'tunis', '51-200', 2015, 1),
    ('Banque Centrale de Tunisie', 'banque-centrale-tunisie', '<EMAIL>', '$company_password', 'Institution financière centrale de la République Tunisienne, responsable de la politique monétaire et de la supervision bancaire.', 'Finance', 'https://bct.gov.tn', '+216 71 340 000', 'Tunis', 'tunis', '501-1000', 1958, 1),
    ('Digital Marketing Pro', 'digital-marketing-pro', '<EMAIL>', '$company_password', 'Agence de marketing digital spécialisée dans les stratégies de communication en ligne et le développement de marques.', 'Marketing', 'https://digitalmarketingpro.tn', '+216 70 987 654', 'Tunis', 'tunis', '11-50', 2018, 1)
");
echo "✅ Sample companies created (3 companies)\n";

// Insert sample users
$user_password = password_hash('user123', PASSWORD_DEFAULT);
$pdo->exec("
    INSERT INTO `users` (`email`, `password`, `first_name`, `last_name`, `phone`, `city`, `governorate`, `education_level`, `experience_years`, `skills`, `email_verified`) VALUES
    ('<EMAIL>', '$user_password', 'Ahmed', 'Ben Ali', '+216 20 123 456', 'Tunis', 'tunis', 'master', 5, 'PHP, JavaScript, MySQL, Laravel, Vue.js', 1),
    ('<EMAIL>', '$user_password', 'Fatma', 'Trabelsi', '+216 25 987 654', 'Sfax', 'sfax', 'bachelor', 3, 'Comptabilité, Excel, SAP, Audit', 1),
    ('<EMAIL>', '$user_password', 'Mohamed', 'Karray', '+216 22 555 777', 'Sousse', 'sousse', 'master', 7, 'Marketing Digital, SEO, Google Ads, Social Media', 1)
");
echo "✅ Sample users created (3 users)\n";

// Insert sample jobs with all required columns
$pdo->exec("
    INSERT INTO `jobs` (`company_id`, `category_id`, `title`, `slug`, `description`, `requirements`, `benefits`, `job_type`, `experience_level`, `education_level`, `location`, `salary_min`, `salary_max`, `application_deadline`, `is_active`, `is_featured`, `views_count`, `published_at`) VALUES
    (1, 1, 'Développeur Full Stack', 'developpeur-full-stack-tunis', 'Nous recherchons un développeur full stack expérimenté pour rejoindre notre équipe dynamique. Vous travaillerez sur des projets innovants utilisant les dernières technologies web.', 'Maîtrise de PHP, JavaScript, MySQL\nExpérience avec Laravel et Vue.js\nConnaissance des API REST\nMaîtrise de Git', 'Salaire compétitif\nAssurance maladie\nFormation continue\nEnvironnement de travail moderne\nPossibilité de télétravail', 'cdi', 'mid', 'bachelor', 'Tunis', 1200.00, 1800.00, '2024-12-31', 1, 1, 156, NOW()),
    (2, 2, 'Comptable Senior', 'comptable-senior-tunis', 'Poste de comptable senior au sein de la Banque Centrale de Tunisie. Responsabilité de la gestion comptable et du reporting financier.', 'Diplôme en comptabilité ou finance\nExpérience minimum 5 ans\nMaîtrise des normes comptables tunisiennes\nExcellente maîtrise d\\'Excel', 'Salaire attractif selon profil\nAvantages sociaux complets\nFormation professionnelle\nEnvironnement stable', 'cdi', 'senior', 'master', 'Tunis', 1500.00, 2200.00, '2024-11-30', 1, 1, 89, NOW()),
    (3, 3, 'Chef de Projet Digital', 'chef-projet-digital-tunis', 'Nous cherchons un chef de projet digital créatif pour gérer nos campagnes marketing et développer notre présence en ligne.', 'Expérience en marketing digital\nMaîtrise des outils Google (Analytics, Ads)\nConnaissance des réseaux sociaux\nCapacités de gestion d\\'équipe', 'Package salarial attractif\nPrimes sur objectifs\nFormation aux nouvelles technologies\nAmbiance de travail créative', 'cdi', 'mid', 'bachelor', 'Tunis', 1000.00, 1500.00, '2024-12-15', 1, 0, 67, NOW()),
    (1, 1, 'Développeur Mobile', 'developpeur-mobile-tunis', 'Développement d\\'applications mobiles natives et hybrides pour iOS et Android.', 'Swift, Kotlin, React Native\nExpérience avec les stores\nConnaissances UI/UX', 'Salaire compétitif\nFormation continue\nProjet innovants', 'cdi', 'mid', 'bachelor', 'Tunis', 1100.00, 1600.00, '2024-12-20', 1, 0, 45, NOW()),
    (2, 2, 'Analyste Financier', 'analyste-financier-tunis', 'Analyse des marchés financiers et support aux décisions d\\'investissement.', 'Master en finance\nCertifications CFA appréciées\nMaîtrise des outils d\\'analyse', 'Salaire attractif\nBonus performance\nFormation continue', 'cdi', 'mid', 'master', 'Tunis', 1300.00, 1900.00, '2024-12-25', 1, 0, 32, NOW())
");
echo "✅ Sample jobs created (5 jobs with SEO URLs)\n";

echo "\n✅ DATABASE INSTALLATION COMPLETED!\n";
echo "===================================\n";

// Verify installation
$stats = [
    'governorates' => $pdo->query("SELECT COUNT(*) FROM governorates")->fetchColumn(),
    'admin_users' => $pdo->query("SELECT COUNT(*) FROM admin_users")->fetchColumn(),
    'job_categories' => $pdo->query("SELECT COUNT(*) FROM job_categories")->fetchColumn(),
    'companies' => $pdo->query("SELECT COUNT(*) FROM companies")->fetchColumn(),
    'users' => $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn(),
    'jobs' => $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn()
];

echo "📊 Database Statistics:\n";
foreach ($stats as $table => $count) {
    echo "   $table: $count records\n";
}

echo "\n🔑 LOGIN CREDENTIALS:\n";
echo "====================\n";
echo "👤 Admin: admin / admin123\n";
echo "🏢 Company: <EMAIL> / company123\n";
echo "👥 User: <EMAIL> / user123\n";

echo "\n🌐 WEBSITE READY:\n";
echo "=================\n";
echo "🏠 Homepage: https://tunisieconcours.org/\n";
echo "💼 Jobs: https://tunisieconcours.org/jobs.php\n";
echo "🔧 Admin: https://tunisieconcours.org/admin-login.php\n";

echo "\n📄 SAMPLE JOB URLS:\n";
echo "===================\n";
echo "🔗 https://tunisieconcours.org/emploi/developpeur-full-stack-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/comptable-senior-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/chef-projet-digital-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/developpeur-mobile-tunis\n";
echo "🔗 https://tunisieconcours.org/emploi/analyste-financier-tunis\n";

echo "\n🎉 ALL TABLES AND COLUMNS CREATED!\n";
echo "==================================\n";
echo "✅ Complete database schema installed\n";
echo "✅ All columns including 'logo' for companies\n";
echo "✅ Sample data inserted\n";
echo "✅ Foreign key relationships established\n";
echo "✅ Indexes created for performance\n";
echo "✅ Ready for production use\n";

echo "\n🇹🇳 tunisieconcours.org Database Ready!\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
?>
