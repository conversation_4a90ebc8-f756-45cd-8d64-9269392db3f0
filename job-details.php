<?php
session_start();
require_once 'config/database.php';
require_once 'includes/seo.php';
require_once 'includes/functions.php';

// Handle both ID and slug parameters
$job_id = 0;
$job_slug = '';

if (isset($_GET['slug']) && !empty($_GET['slug'])) {
    $job_slug = sanitize($_GET['slug']);
    
    // Get job by slug
    $job = $database->fetch("
        SELECT j.*, c.name as company_name, c.logo as company_logo, c.description as company_description, 
               c.website as company_website, cat.name as category_name
        FROM jobs j
        JOIN companies c ON j.company_id = c.id
        LEFT JOIN job_categories cat ON j.category_id = cat.id
        WHERE j.slug = ? AND j.is_active = 1 AND c.is_active = 1
    ", [$job_slug]);

    if ($job) {
        $job_id = $job['id'];
    }
} else if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $job_id = (int)$_GET['id'];
    
    // If we have an ID, redirect to SEO-friendly URL
    $job_for_redirect = $database->fetch("SELECT slug FROM jobs WHERE id = ? AND is_active = 1", [$job_id]);
    if ($job_for_redirect && !empty($job_for_redirect['slug'])) {
        header('Location: /emploi/' . $job_for_redirect['slug'], true, 301);
        exit;
    }
    
    // Get job by ID (fallback)
    $job = $database->fetch("
        SELECT j.*, c.name as company_name, c.logo as company_logo, c.description as company_description,
               c.website as company_website, cat.name as category_name
        FROM jobs j
        JOIN companies c ON j.company_id = c.id
        LEFT JOIN job_categories cat ON j.category_id = cat.id
        WHERE j.id = ? AND j.is_active = 1 AND c.is_active = 1
    ", [$job_id]);
} else {
    header('Location: jobs.php');
    exit;
}

if (!$job) {
    header('Location: jobs.php?error=job_not_found');
    exit;
}

// Update view count
$database->query("UPDATE jobs SET views_count = views_count + 1 WHERE id = ?", [$job_id]);

// Check if user is logged in and has already applied
$has_applied = false;
$user_logged_in = isset($_SESSION['logged_in']) && $_SESSION['user_type'] === 'candidate';

if ($user_logged_in) {
    $user_id = $_SESSION['user_id'];
    $application = $database->fetch("SELECT id FROM job_applications WHERE job_id = ? AND user_id = ?", [$job_id, $user_id]);
    $has_applied = !empty($application);
}

// Handle job application
$success = '';
$errors = [];

if ($_POST && isset($_POST['apply']) && $user_logged_in && !$has_applied) {
    $cover_letter = sanitize($_POST['cover_letter'] ?? '');
    
    // Validation
    if (empty($cover_letter)) {
        $errors[] = 'La lettre de motivation est requise';
    }
    
    // Handle CV upload
    $cv_filename = null;
    if (isset($_FILES['cv_file']) && $_FILES['cv_file']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/cvs/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $file_extension = strtolower(pathinfo($_FILES['cv_file']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['pdf', 'doc', 'docx'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            if ($_FILES['cv_file']['size'] <= 5 * 1024 * 1024) { // 5MB max
                $cv_filename = uniqid() . '_' . time() . '.' . $file_extension;
                $upload_path = $upload_dir . $cv_filename;
                
                if (!move_uploaded_file($_FILES['cv_file']['tmp_name'], $upload_path)) {
                    $errors[] = 'Erreur lors du téléchargement du CV';
                    $cv_filename = null;
                }
            } else {
                $errors[] = 'Le fichier CV ne doit pas dépasser 5MB';
            }
        } else {
            $errors[] = 'Format de fichier non autorisé. Utilisez PDF, DOC ou DOCX';
        }
    }
    
    if (empty($errors)) {
        $application_data = [
            'job_id' => $job_id,
            'user_id' => $user_id,
            'cover_letter' => $cover_letter,
            'cv_file' => $cv_filename,
            'status' => 'pending',
            'applied_at' => date('Y-m-d H:i:s')
        ];
        
        $application_id = $database->insert('job_applications', $application_data);
        
        if ($application_id) {
            // Update applications count
            $database->query("UPDATE jobs SET applications_count = applications_count + 1 WHERE id = ?", [$job_id]);
            
            $success = 'Votre candidature a été envoyée avec succès!';
            $has_applied = true;
            
            // Clear POST data
            $_POST = [];
        } else {
            $errors[] = 'Erreur lors de l\'envoi de la candidature';
        }
    }
}

// Job types in French
$job_types = [
    'cdi' => 'CDI',
    'cdd' => 'CDD',
    'stage' => 'Stage',
    'freelance' => 'Freelance',
    'temps_partiel' => 'Temps partiel',
    'interim' => 'Intérim',
    'apprentissage' => 'Apprentissage',
    'saisonnier' => 'Saisonnier'
];

// Initialize SEO
$seo = new SEOManager();

// Generate SEO data for this job
$seo_data = [
    'title' => htmlspecialchars($job['title']) . ' - ' . htmlspecialchars($job['company_name']) . ' | Concours Tunisie',
    'description' => 'Postulez pour le poste de ' . htmlspecialchars($job['title']) . ' chez ' . htmlspecialchars($job['company_name']) . ' à ' . htmlspecialchars($job['location']) . '. ' . substr(strip_tags($job['description']), 0, 150) . '...',
    'keywords' => 'emploi ' . strtolower($job['title']) . ', ' . strtolower($job['location']) . ', ' . strtolower($job['company_name']) . ', recrutement tunisie',
    'canonical' => 'https://tunisieconcours.org/emploi/' . $job['slug'],
    'og_title' => htmlspecialchars($job['title']) . ' - ' . htmlspecialchars($job['company_name']),
    'og_description' => 'Postulez pour le poste de ' . htmlspecialchars($job['title']) . ' chez ' . htmlspecialchars($job['company_name']) . ' à ' . htmlspecialchars($job['location']),
    'og_url' => 'https://tunisieconcours.org/emploi/' . $job['slug'],
    'og_image' => !empty($job['company_logo']) ? 'https://tunisieconcours.org/uploads/logos/' . $job['company_logo'] : 'https://tunisieconcours.org/assets/images/job-default.jpg'
];

// Set page variables for header
$page_title = $seo_data['title'];
$seo_manager = $seo;

// Set additional CSS for this page
$additional_css = '
<style>
    .job-details-container {
        padding-top: 100px;
        min-height: 100vh;
        background: #f5f5f5;
    }

    .job-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 50px 0;
        margin-bottom: 40px;
    }

    .job-title-section {
        display: flex;
        align-items: center;
        gap: 30px;
    }

    .company-logo {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        object-fit: cover;
        background: white;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .company-logo img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 5px;
    }

    .company-logo i {
        font-size: 2rem;
        color: #667eea;
    }

    .job-meta {
        display: flex;
        gap: 30px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(255,255,255,0.1);
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .job-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 40px;
        margin-bottom: 40px;
    }

    .job-main {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .job-sidebar {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        height: fit-content;
    }

    .section-title {
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #667eea;
    }

    .btn-apply {
        background: #667eea;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: background 0.3s;
        width: 100%;
        text-align: center;
    }

    .btn-apply:hover {
        background: #5a6fd8;
        text-decoration: none;
        color: white;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }

    .form-group input, .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
    }

    .form-group textarea {
        min-height: 120px;
        resize: vertical;
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }

    @media (max-width: 768px) {
        .job-content {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .job-title-section {
            flex-direction: column;
            text-align: center;
            gap: 20px;
        }
        
        .job-meta {
            justify-content: center;
        }
    }
</style>';

include 'includes/header.php';
?>

<?php echo $additional_css; ?>
<main class="job-details-container">
    <!-- Breadcrumbs -->
    <div class="breadcrumbs">
        <a href="/jobs.php">Emplois</a> <span>/</span>
        <a href="/companies.php?company=<?php echo urlencode($job['company_name']); ?>"><?php echo htmlspecialchars($job['company_name']); ?></a> <span>/</span>
        <span><?php echo htmlspecialchars($job['title']); ?></span>
    </div>
    <!-- Hero Section -->
    <section class="job-header">
        <div class="container">
            <div class="job-title-section">
                <div class="company-logo">
                    <?php if (!empty($job['company_logo'])): ?>
                        <img src="uploads/logos/<?php echo htmlspecialchars($job['company_logo']); ?>" alt="Logo entreprise">
                    <?php else: ?>
                        <i class="fas fa-building"></i>
                    <?php endif; ?>
                </div>
                <div>
                    <h1 class="job-title"><?php echo htmlspecialchars($job['title']); ?></h1>
                    <div class="job-meta">
                        <span class="meta-item"><i class="fas fa-building"></i> <?php echo htmlspecialchars($job['company_name']); ?></span>
                        <span class="meta-item"><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($job['location']); ?></span>
                        <span class="meta-item"><i class="fas fa-clock"></i> <?php echo strtoupper($job['job_type']); ?></span>
                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                            <span class="meta-item"><i class="fas fa-money-bill-wave"></i> <?php echo number_format($job['salary_min']); ?> - <?php echo number_format($job['salary_max']); ?> TND</span>
                        <?php endif; ?>
                        <span class="meta-item"><i class="fas fa-calendar"></i> Publié le <?php echo date('d/m/Y', strtotime($job['published_at'])); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Main Content -->
    <div class="container">
        <div class="job-content">
            <div class="job-main">
                <h2>Description du poste</h2>
                <div class="job-description-text"><?php echo nl2br($job['description']); ?></div>
                <h3>Exigences</h3>
                <div class="job-requirements-text"><?php echo nl2br($job['requirements']); ?></div>
                <?php if (!empty($job['benefits'])): ?>
                <h3>Avantages</h3>
                <div class="job-benefits-text"><?php echo nl2br($job['benefits']); ?></div>
                <?php endif; ?>
            </div>
            <aside class="job-sidebar">
                <div class="sidebar-card">
                    <h4>Résumé du poste</h4>
                    <ul class="sidebar-list">
                        <li><strong>Type:</strong> <?php echo strtoupper($job['job_type']); ?></li>
                        <li><strong>Catégorie:</strong> <?php echo htmlspecialchars($job['category_name']); ?></li>
                        <li><strong>Lieu:</strong> <?php echo htmlspecialchars($job['location']); ?></li>
                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                        <li><strong>Salaire:</strong> <?php echo number_format($job['salary_min']); ?> - <?php echo number_format($job['salary_max']); ?> TND</li>
                        <?php endif; ?>
                        <li><strong>Expérience:</strong> <?php echo htmlspecialchars($job['experience_level']); ?></li>
                        <li><strong>Éducation:</strong> <?php echo htmlspecialchars($job['education_level']); ?></li>
                        <li><strong>Publié:</strong> <?php echo date('d/m/Y', strtotime($job['published_at'])); ?></li>
                    </ul>
                </div>
                <div class="sidebar-card company-info-card">
                    <h4>Entreprise</h4>
                    <div class="company-info-logo">
                        <?php if (!empty($job['company_logo'])): ?>
                            <img src="uploads/logos/<?php echo htmlspecialchars($job['company_logo']); ?>" alt="Logo entreprise">
                        <?php else: ?>
                            <i class="fas fa-building"></i>
                        <?php endif; ?>
                    </div>
                    <div class="company-info-name"><?php echo htmlspecialchars($job['company_name']); ?></div>
                    <div class="company-info-desc"><?php echo nl2br(htmlspecialchars($job['company_description'])); ?></div>
                    <?php if (!empty($job['company_website'])): ?>
                        <a href="<?php echo htmlspecialchars($job['company_website']); ?>" target="_blank" class="company-website-link"><i class="fas fa-globe"></i> Site web</a>
                    <?php endif; ?>
                </div>
                <?php if ($user_logged_in && !$has_applied): ?>
                <a href="#apply-form" class="btn btn-apply-sidebar">Postuler à ce poste</a>
                <?php endif; ?>
            </aside>
        </div>
        <!-- Application Form -->
        <?php if ($user_logged_in): ?>
        <div id="apply-form" class="application-form-section">
            <h3>Postuler à ce poste</h3>
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>
            <?php if (!$has_applied): ?>
            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="cover_letter">Lettre de motivation *</label>
                    <textarea id="cover_letter" name="cover_letter" required><?php echo htmlspecialchars($_POST['cover_letter'] ?? ''); ?></textarea>
                </div>
                <div class="form-group">
                    <label for="cv_file">CV (PDF, DOC, DOCX, max 5MB)</label>
                    <input type="file" id="cv_file" name="cv_file" accept=".pdf,.doc,.docx">
                </div>
                <button type="submit" name="apply" class="btn btn-primary btn-apply-main">Envoyer ma candidature</button>
            </form>
            <?php else: ?>
                <div class="alert alert-info">Vous avez déjà postulé à cette offre.</div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</main>
<style>
.breadcrumbs {
  margin: 24px 0 0 0;
  font-size: 1rem;
  color: #64748b;
}
.breadcrumbs a {
  color: #2563eb;
  text-decoration: none;
}
.breadcrumbs span {
  margin: 0 6px;
}
.job-header {
  background: linear-gradient(90deg,#2563eb 0%,#1d4ed8 100%);
  color: #fff;
  padding: 48px 0 32px 0;
  margin-bottom: 32px;
}
.job-title-section {
  display: flex;
  align-items: center;
  gap: 32px;
}
.company-logo {
  width: 90px;
  height: 90px;
  border-radius: 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(44,90,160,0.08);
  overflow: hidden;
}
.company-logo img {
  width: 70px;
  height: 70px;
  object-fit: cover;
  border-radius: 8px;
}
.company-logo i {
  font-size: 2.5rem;
  color: #2563eb;
}
.job-title {
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0 0 10px 0;
  color: #fff;
}
.job-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
  margin-top: 10px;
}
.meta-item {
  background: rgba(255,255,255,0.13);
  padding: 7px 16px;
  border-radius: 16px;
  font-size: 1rem;
  color: #f1f5f9;
  display: flex;
  align-items: center;
  gap: 7px;
}
.job-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}
.job-main {
  background: #fff;
  border-radius: 15px;
  padding: 32px;
  box-shadow: 0 5px 15px rgba(44,90,160,0.08);
}
.job-main h2, .job-main h3 {
  color: #2563eb;
  margin-top: 0;
}
.job-description-text, .job-requirements-text, .job-benefits-text {
  color: #334155;
  font-size: 1.08rem;
  margin-bottom: 18px;
}
.job-sidebar {
  background: #fff;
  border-radius: 15px;
  padding: 28px;
  box-shadow: 0 5px 15px rgba(44,90,160,0.08);
  height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.sidebar-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 18px 16px;
  margin-bottom: 18px;
  box-shadow: 0 2px 8px rgba(44,90,160,0.04);
}
.sidebar-card h4 {
  color: #1e293b;
  margin-top: 0;
}
.sidebar-list {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #334155;
  font-size: 1rem;
}
.sidebar-list li {
  margin-bottom: 8px;
}
.company-info-card {
  text-align: center;
}
.company-info-logo {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #fff;
  margin: 0 auto 10px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 6px rgba(44,90,160,0.08);
  overflow: hidden;
}
.company-info-logo img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 6px;
}
.company-info-logo i {
  font-size: 2rem;
  color: #2563eb;
}
.company-info-name {
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 6px;
}
.company-info-desc {
  color: #64748b;
  font-size: 0.98rem;
  margin-bottom: 10px;
}
.company-website-link {
  color: #2563eb;
  text-decoration: none;
  font-size: 0.98rem;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.btn.btn-apply-sidebar {
  background: linear-gradient(90deg,#2563eb 0%,#1d4ed8 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 22px;
  font-weight: 700;
  font-size: 1.1rem;
  margin-top: 18px;
  text-align: center;
  display: block;
  transition: background 0.2s;
}
.btn.btn-apply-sidebar:hover {
  background: linear-gradient(90deg,#1d4ed8 0%,#2563eb 100%);
  color: #fff;
}
.application-form-section {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(44,90,160,0.08);
  padding: 32px;
  margin-top: 32px;
}
.btn-apply-main {
  background: linear-gradient(90deg,#2563eb 0%,#1d4ed8 100%);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-weight: 700;
  font-size: 1.1rem;
  margin-top: 10px;
  transition: background 0.2s;
}
.btn-apply-main:hover {
  background: linear-gradient(90deg,#1d4ed8 0%,#2563eb 100%);
  color: #fff;
}
@media (max-width: 900px) {
  .job-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  .job-sidebar {
    padding: 18px;
  }
}
@media (max-width: 600px) {
  .job-header {
    padding: 32px 0 18px 0;
  }
  .job-title-section {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  .job-main, .job-sidebar, .application-form-section {
    padding: 14px;
  }
}
</style>

<?php include 'includes/footer.php'; ?>
