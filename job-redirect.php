<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$job_id = (int)($_GET['id'] ?? 0);

if ($job_id <= 0) {
    header('Location: jobs.php');
    exit;
}

// Get job details to generate SEO URL
$job = $database->fetch("
    SELECT j.slug, j.title
    FROM jobs j
    WHERE j.id = ? AND j.is_active = 1
", [$job_id]);

if (!$job) {
    header('Location: jobs.php?error=job_not_found');
    exit;
}

// If no slug exists, generate one
if (empty($job['slug'])) {
    $slug = generateSlug($job['title'] . '-' . $job_id);
    $database->update('jobs', ['slug' => $slug], 'id = ?', [$job_id]);
} else {
    $slug = $job['slug'];
}

// Redirect to SEO-friendly URL
header('Location: /emploi/' . $slug, true, 301);
exit;
?>
