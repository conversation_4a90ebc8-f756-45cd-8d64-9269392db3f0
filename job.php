<?php
require_once 'config/database.php';

// Get the slug from URL parameter or path
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    // Try to get from URL path as fallback
    $request_uri = $_SERVER['REQUEST_URI'];
    $path = parse_url($request_uri, PHP_URL_PATH);
    $slug = str_replace('/job/', '', $path);
    $slug = trim($slug, '/');
}

if (empty($slug)) {
    header('Location: jobs.php');
    exit;
}

// Parse the slug (format: category-slug-job-title-slug)
$parts = explode('-', $slug);

// Try to find the job by reconstructing possible combinations
$job = null;
$job_id = null;

// First, try to find by exact slug match
$job = $database->fetch("
    SELECT j.*, c.name as company_name, c.logo as company_logo, c.website as company_website,
           cat.name as category_name, cat.slug as category_slug
    FROM jobs j
    JOIN companies c ON j.company_id = c.id
    JOIN job_categories cat ON j.category_id = cat.id
    WHERE j.slug = ? AND j.is_active = 1 AND c.is_active = 1
", [$slug]);

// If not found by slug, try to extract ID from the end of the slug
if (!$job) {
    // Look for patterns like "category-title-123" where 123 is the job ID
    if (preg_match('/(\d+)$/', $slug, $matches)) {
        $potential_id = $matches[1];
        $job = $database->fetch("
            SELECT j.*, c.name as company_name, c.logo as company_logo, c.website as company_website,
                   cat.name as category_name, cat.slug as category_slug
            FROM jobs j
            JOIN companies c ON j.company_id = c.id
            JOIN job_categories cat ON j.category_id = cat.id
            WHERE j.id = ? AND j.is_active = 1 AND c.is_active = 1
        ", [$potential_id]);
    }
}

// If still not found, try to find by title similarity
if (!$job) {
    // Convert slug back to title-like format
    $title_guess = str_replace('-', ' ', $slug);
    $job = $database->fetch("
        SELECT j.*, c.name as company_name, c.logo as company_logo, c.website as company_website,
               cat.name as category_name, cat.slug as category_slug
        FROM jobs j
        JOIN companies c ON j.company_id = c.id
        JOIN job_categories cat ON j.category_id = cat.id
        WHERE j.title LIKE ? AND j.is_active = 1 AND c.is_active = 1
        LIMIT 1
    ", ['%' . $title_guess . '%']);
}

if (!$job) {
    header('HTTP/1.0 404 Not Found');
    header('Location: jobs.php?error=job_not_found');
    exit;
}

$job_id = $job['id'];

// Generate the correct canonical URL
function generateJobSlug($job) {
    $category_slug = !empty($job['category_slug']) ? $job['category_slug'] : 'emploi';
    $title_slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $job['title'])));
    return $category_slug . '-' . $title_slug;
}

$canonical_slug = generateJobSlug($job);

// Set the job ID for job-details.php and prevent redirect loop
$_GET['id'] = $job_id;
$_GET['from_seo'] = true;

// Include the job details logic
include 'job-details.php';
?>
