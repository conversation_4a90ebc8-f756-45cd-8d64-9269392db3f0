<?php
session_start();
require_once 'config/database.php';
require_once 'includes/seo.php';
require_once 'includes/functions.php';

// Get search parameters
$search = sanitize($_GET['search'] ?? '');
$category = (int)($_GET['category'] ?? 0);
$location = sanitize($_GET['location'] ?? '');
$job_type = sanitize($_GET['job_type'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build search query
$where_conditions = ["j.is_active = 1"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(j.title LIKE ? OR j.description LIKE ? OR c.name LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if ($category > 0) {
    $where_conditions[] = "j.category_id = ?";
    $params[] = $category;
}

if (!empty($location)) {
    $where_conditions[] = "j.location LIKE ?";
    $params[] = "%$location%";
}

if (!empty($job_type)) {
    $where_conditions[] = "j.job_type = ?";
    $params[] = $job_type;
}

$where_clause = implode(" AND ", $where_conditions);

// Get total count
$count_sql = "SELECT COUNT(*) as total FROM jobs j
              LEFT JOIN companies c ON j.company_id = c.id
              WHERE $where_clause";
$total_jobs = $database->fetch($count_sql, $params)['total'];
$total_pages = ceil($total_jobs / $per_page);

// Get jobs
$jobs_sql = "SELECT j.*, c.name as company_name, c.is_verified, cat.name as category_name
             FROM jobs j
             LEFT JOIN companies c ON j.company_id = c.id
             LEFT JOIN job_categories cat ON j.category_id = cat.id
             WHERE $where_clause
             ORDER BY j.is_featured DESC, j.published_at DESC
             LIMIT $per_page OFFSET $offset";

$jobs = $database->fetchAll($jobs_sql, $params);

// Get categories for filter
$categories = $database->fetchAll("SELECT * FROM job_categories WHERE is_active = 1 ORDER BY name");

// Initialize SEO
$seo = new SEOManager();

// SEO data
$seo_data = [
    "title" => "Offres d'Emploi en Tunisie - tunisieconcours.org",
    "description" => "Découvrez les meilleures offres d'emploi en Tunisie. CDI, CDD, stages et freelance dans tous les secteurs.",
    "keywords" => "emploi tunisie, travail tunisie, offres emploi, jobs tunisia, recrutement",
    "canonical" => "https://tunisieconcours.org/jobs.php",
    "og_title" => "Offres d'Emploi en Tunisie",
    "og_description" => "Découvrez les meilleures offres d'emploi en Tunisie. CDI, CDD, stages et freelance dans tous les secteurs.",
    "og_url" => "https://tunisieconcours.org/jobs.php",
    "og_image" => "https://tunisieconcours.org/assets/images/jobs-og.jpg"
];

if (!empty($search)) {
    $seo_data['title'] = "Emplois \"$search\" en Tunisie - tunisieconcours.org";
    $seo_data['og_title'] = "Emplois \"$search\" en Tunisie";
}

// Set page variables for header
$page_title = $seo_data['title'];
$seo_manager = $seo;
?>
<?php include "includes/header.php"; ?>
    
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-briefcase"></i> Offres d'Emploi</h1>
                <p>Trouvez votre emploi idéal parmi <?php echo number_format($total_jobs); ?> offres disponibles</p>
            </div>
            
            <!-- Search Form -->
            <div class="search-section">
                <form method="GET" class="search-form">
                    <div class="search-row">
                        <div class="search-field">
                            <input type="text" name="search" placeholder="Titre du poste, entreprise..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="search-field">
                            <select name="category">
                                <option value="">Toutes les catégories</option>
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="search-field">
                            <input type="text" name="location" placeholder="Ville..." value="<?php echo htmlspecialchars($location); ?>">
                        </div>
                        <div class="search-field">
                            <select name="job_type">
                                <option value="">Type de contrat</option>
                                <option value="cdi" <?php echo $job_type == 'cdi' ? 'selected' : ''; ?>>CDI</option>
                                <option value="cdd" <?php echo $job_type == 'cdd' ? 'selected' : ''; ?>>CDD</option>
                                <option value="stage" <?php echo $job_type == 'stage' ? 'selected' : ''; ?>>Stage</option>
                                <option value="freelance" <?php echo $job_type == 'freelance' ? 'selected' : ''; ?>>Freelance</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Results -->
            <div class="results-section">
                <div class="results-header">
                    <h2><?php echo number_format($total_jobs); ?> offres trouvées</h2>
                </div>
                
                <div class="jobs-grid modern-jobs-grid">
                    <?php if (empty($jobs)): ?>
                        <div class="no-results">
                            <i class="fas fa-search"></i>
                            <h3>Aucune offre trouvée</h3>
                            <p>Essayez de modifier vos critères de recherche</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($jobs as $job): ?>
                            <div class="job-card modern-job-card" onclick="window.location.href='emploi/<?php echo $job['slug']; ?>'">
                                <div class="job-card-header">
                                    <?php if (!empty($job['company_logo'])): ?>
                                        <img src="uploads/logos/<?php echo htmlspecialchars($job['company_logo']); ?>" alt="Logo entreprise" class="job-company-logo">
                                    <?php else: ?>
                                        <div class="job-company-logo-placeholder"><i class="fas fa-building"></i></div>
                                    <?php endif; ?>
                                    <div class="job-card-title-group">
                                        <h3 class="job-card-title"><?php echo htmlspecialchars($job['title']); ?></h3>
                                        <div class="job-card-badges">
                                            <?php if ($job['is_featured']): ?>
                                                <span class="badge badge-featured">En vedette</span>
                                            <?php endif; ?>
                                            <?php if ($job['is_verified']): ?>
                                                <span class="badge badge-verified"><i class="fas fa-check-circle"></i> Vérifiée</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="job-card-meta">
                                    <span><i class="fas fa-building"></i> <?php echo htmlspecialchars($job['company_name']); ?></span>
                                    <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($job['location']); ?></span>
                                    <span><i class="fas fa-clock"></i> <?php echo strtoupper($job['job_type']); ?></span>
                                    <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                        <span><i class="fas fa-money-bill-wave"></i> <?php echo number_format($job['salary_min']); ?> - <?php echo number_format($job['salary_max']); ?> TND</span>
                                    <?php endif; ?>
                                </div>
                                <div class="job-card-description">
                                    <?php echo substr(strip_tags($job['description']), 0, 120); ?>...
                                </div>
                                <div class="job-card-footer">
                                    <span class="job-date"><i class="fas fa-calendar"></i> <?php echo date('d/m/Y', strtotime($job['published_at'])); ?></span>
                                    <span class="job-views"><i class="fas fa-eye"></i> <?php echo $job['views_count']; ?> vues</span>
                                    <a href="emploi/<?php echo $job['slug']; ?>" class="btn btn-view-offer" onclick="event.stopPropagation();">Voir l'offre <i class="fas fa-arrow-right"></i></a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="btn btn-outline">
                                <i class="fas fa-chevron-left"></i> Précédent
                            </a>
                        <?php endif; ?>
                        
                        <span class="page-info">
                            Page <?php echo $page; ?> sur <?php echo $total_pages; ?>
                        </span>
                        
                        <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="btn btn-outline">
                                Suivant <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
    
    <?php include "includes/footer.php"; ?>
</body>
</html>