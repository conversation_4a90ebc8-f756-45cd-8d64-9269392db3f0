-- LOCAL DATABASE SETUP FOR TUNISIECONCOURS.ORG
-- Run this in phpMyAdmin to create database and user locally

-- Create database
CREATE DATABASE IF NOT EXISTS `tunisieconcours_dirr` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (for local development)
CREATE USER IF NOT EXISTS 'tunisieconcours_dirr'@'localhost' IDENTIFIED BY '&w],o=IuJmAS.ar~';

-- Grant privileges
GRANT ALL PRIVILEGES ON `tunisieconcours_dirr`.* TO 'tunisieconcours_dirr'@'localhost';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the database
USE `tunisieconcours_dirr`;

-- Show success message
SELECT 'Database and user created successfully!' as Status;
SELECT 'Now run: php complete_setup.php' as NextStep;
