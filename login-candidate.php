<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = '';

// Check for logout message
if (isset($_GET['message']) && $_GET['message'] === 'logged_out') {
    $success = 'Vous avez été déconnecté avec succès.';
}

// Check if already logged in
if (isset($_SESSION['logged_in']) && $_SESSION['user_type'] === 'candidate') {
    header('Location: candidate-dashboard.php');
    exit;
}

if ($_POST) {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Validation
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (empty($password)) $errors[] = 'Le mot de passe est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    
    if (empty($errors)) {
        $user = $database->fetch("SELECT id, email, password, CONCAT(first_name, ' ', last_name) as name, first_name, last_name, is_active FROM users WHERE email = ?", [$email]);
        
        if ($user && verifyPassword($password, $user['password'])) {
            if ($user['is_active']) {
                // Start session and store user data
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['last_name'] = $user['last_name'];
                $_SESSION['user_type'] = 'candidate';
                $_SESSION['logged_in'] = true;

                // Update last login
                $database->query("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);

                header('Location: candidate-dashboard.php');
                exit;
            } else {
                $errors[] = 'Votre compte est en attente d\'approbation par l\'administrateur. Vous recevrez un email une fois votre compte activé.';
            }
        } else {
            $errors[] = 'Email ou mot de passe incorrect';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion Candidat - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .login-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 450px;
            width: 100%;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .user-badge {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .btn-login {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        
        .btn-login:hover {
            background: #5a6fd8;
        }
        
        .login-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .login-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .login-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="login-header">
                <h1><i class="fas fa-user"></i> Espace Candidat</h1>
                <div class="user-badge">
                    <i class="fas fa-graduation-cap"></i>
                    Connexion Candidat
                </div>
                <p>Connectez-vous pour accéder à votre espace personnel</p>
            </div>
            
            <?php if (!empty($success)): ?>
                <div class="alert" style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb;">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="email">Adresse email</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt"></i>
                    Se connecter
                </button>
            </form>
            
            <div class="login-links">
                <p>
                    <a href="register-candidate.php">Créer un compte candidat</a> |
                    <a href="login-company.php">Espace entreprise</a> |
                    <a href="index.php">Retour à l'accueil</a>
                </p>
                <small style="color: #999;">Vous êtes une entreprise ? <a href="login-company.php">Connectez-vous ici</a></small>
            </div>
        </div>
    </div>
</body>
</html>
