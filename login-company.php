<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = '';

// Check for logout message
if (isset($_GET['message']) && $_GET['message'] === 'logged_out') {
    $success = 'Vous avez été déconnecté avec succès.';
}

// Check if already logged in
if (isset($_SESSION['logged_in']) && $_SESSION['user_type'] === 'company') {
    header('Location: company-dashboard.php');
    exit;
}

if ($_POST) {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // Validation
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (empty($password)) $errors[] = 'Le mot de passe est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    
    if (empty($errors)) {
        $company = $database->fetch("SELECT id, email, password, name, is_active, is_verified FROM companies WHERE email = ?", [$email]);
        
        if ($company && verifyPassword($password, $company['password'])) {
            if ($company['is_active']) {
                // Start session and store company data
                $_SESSION['user_id'] = $company['id'];
                $_SESSION['user_email'] = $company['email'];
                $_SESSION['user_name'] = $company['name'];
                $_SESSION['company_name'] = $company['name'];
                $_SESSION['is_verified'] = $company['is_verified'];
                $_SESSION['user_type'] = 'company';
                $_SESSION['logged_in'] = true;

                // Update last login
                $database->query("UPDATE companies SET last_login = NOW() WHERE id = ?", [$company['id']]);

                header('Location: company-dashboard.php');
                exit;
            } else {
                $errors[] = 'Votre compte entreprise est en attente d\'approbation par l\'administrateur. Vous recevrez un email une fois votre compte activé.';
            }
        } else {
            $errors[] = 'Email ou mot de passe incorrect';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion Entreprise - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 20px;
        }
        
        .login-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 450px;
            width: 100%;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #f5576c;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .company-badge {
            background: #f5576c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #f5576c;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .btn-login {
            background: #f5576c;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        
        .btn-login:hover {
            background: #e74c3c;
        }
        
        .login-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .login-links a {
            color: #f5576c;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .login-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="login-header">
                <h1><i class="fas fa-building"></i> Espace Entreprise</h1>
                <div class="company-badge">
                    <i class="fas fa-briefcase"></i>
                    Connexion Entreprise
                </div>
                <p>Connectez-vous pour gérer vos offres d'emploi</p>
            </div>
            
            <?php if (!empty($success)): ?>
                <div class="alert" style="background: #d4edda; color: #155724; border: 1px solid #c3e6cb;">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="email">Adresse email</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn-login">
                    <i class="fas fa-sign-in-alt"></i>
                    Se connecter
                </button>
            </form>
            
            <div class="login-links">
                <p>
                    <a href="register-company.php">Créer un compte entreprise</a> |
                    <a href="login-candidate.php">Espace candidat</a> |
                    <a href="index.php">Retour à l'accueil</a>
                </p>
                <small style="color: #999;">Vous êtes un candidat ? <a href="login-candidate.php">Connectez-vous ici</a></small>
            </div>
        </div>
    </div>
</body>
</html>
