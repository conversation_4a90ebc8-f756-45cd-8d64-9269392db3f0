<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/seo.php';

$errors = [];
$success = '';

// Redirect to appropriate login page based on user preference
if (isset($_GET['type'])) {
    if ($_GET['type'] === 'company') {
        header('Location: login-company.php');
        exit;
    } else {
        header('Location: login-candidate.php');
        exit;
    }
}

// If no type specified, show selection page
if (!$_POST) {
    // Show login type selection
    ?>
    <!DOCTYPE html>
    <html lang="fr" dir="ltr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Connexion - Concours Tunisie</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="assets/css/style.css">
        <style>
            .login-selection {
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 20px;
            }

            .selection-container {
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 40px;
                max-width: 600px;
                width: 100%;
                text-align: center;
            }

            .selection-container h1 {
                color: #333;
                margin-bottom: 10px;
            }

            .selection-container p {
                color: #666;
                margin-bottom: 40px;
            }

            .login-options {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 30px;
            }

            .login-option {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 15px;
                padding: 30px 20px;
                text-decoration: none;
                color: #333;
                transition: all 0.3s;
            }

            .login-option:hover {
                border-color: #667eea;
                transform: translateY(-5px);
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                text-decoration: none;
                color: #333;
            }

            .login-option i {
                font-size: 3rem;
                margin-bottom: 20px;
                color: #667eea;
            }

            .login-option h3 {
                margin-bottom: 10px;
                color: #333;
            }

            .login-option p {
                margin: 0;
                font-size: 0.9rem;
                color: #666;
            }

            .company-option i {
                color: #f5576c;
            }

            .company-option:hover {
                border-color: #f5576c;
            }
        </style>
    </head>
    <body>
        <div class="login-selection">
            <div class="selection-container">
                <h1><i class="fas fa-sign-in-alt"></i> Connexion</h1>
                <p>Choisissez votre type de compte pour vous connecter</p>

                <div class="login-options">
                    <a href="login-candidate.php" class="login-option">
                        <i class="fas fa-user-graduate"></i>
                        <h3>Candidat</h3>
                        <p>Accédez à votre espace personnel pour postuler aux offres d'emploi</p>
                    </a>

                    <a href="login-company.php" class="login-option company-option">
                        <i class="fas fa-building"></i>
                        <h3>Entreprise</h3>
                        <p>Gérez vos offres d'emploi et consultez les candidatures</p>
                    </a>
                </div>

                <div style="margin-top: 30px;">
                    <a href="index.php" style="color: #666; text-decoration: none;">
                        <i class="fas fa-arrow-left"></i> Retour à l'accueil
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

if ($_POST) {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $user_type = sanitize($_POST['user_type'] ?? 'candidate');
    
    // Validation
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (empty($password)) $errors[] = 'Le mot de passe est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    
    if (empty($errors)) {
        $table = ($user_type === 'company') ? 'companies' : 'users';
        $nameField = ($user_type === 'company') ? 'name' : 'CONCAT(first_name, " ", last_name) as name';
        
        $user = $database->fetch("SELECT id, email, password, {$nameField}, is_active FROM {$table} WHERE email = ?", [$email]);
        
        if ($user && verifyPassword($password, $user['password'])) {
            if ($user['is_active']) {
                // Start session and store user data
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['user_type'] = $user_type;
                $_SESSION['logged_in'] = true;

                // Redirect based on user type
                if ($user_type === 'company') {
                    header('Location: company-dashboard.php');
                } else {
                    header('Location: user-dashboard.php');
                }
                exit;
            } else {
                $errors[] = 'Votre compte est en attente d\'approbation par l\'administrateur. Vous recevrez un email une fois votre compte activé.';
            }
        } else {
            $errors[] = 'Email ou mot de passe incorrect';
        }
    }
}

// Generate SEO data for login page
$seo_data = [
    'title' => 'Connexion - tunisieconcours.org Offres d\'Emploi',
    'description' => 'Connectez-vous à votre compte tunisieconcours.org pour accéder aux offres d\'emploi, postuler aux jobs et gérer votre profil professionnel en Tunisie.',
    'keywords' => 'connexion, login, emploi tunisie, compte emploi, tunisieconcours',
    'canonical' => 'https://tunisieconcours.org/login.php',
    'og_title' => 'Connexion - Emploi Tunisie',
    'og_description' => 'Connectez-vous pour accéder aux meilleures offres d\'emploi en Tunisie',
    'og_url' => 'https://tunisieconcours.org/login.php',
    'og_image' => 'https://tunisieconcours.org/assets/images/logo-og.jpg'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php $seo->renderMetaTags($seo_data); ?>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            padding: 20px;
        }
        
        .login-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 450px;
            width: 100%;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #2c5aa0;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .user-type-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
        }
        
        .user-type-option {
            flex: 1;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 600;
        }
        
        .user-type-option.active {
            border-color: #2c5aa0;
            background: #f8f9ff;
            color: #2c5aa0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .forgot-password {
            text-align: center;
            margin: 20px 0;
        }
        
        .forgot-password a {
            color: #2c5aa0;
            text-decoration: none;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="login-header">
                <h1><i class="fas fa-briefcase"></i> Concours Tunisie</h1>
                <p>Connectez-vous à votre compte</p>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" id="loginForm">
                <div class="user-type-selector">
                    <div class="user-type-option active" data-type="candidate">
                        <i class="fas fa-user"></i> Candidat
                    </div>
                    <div class="user-type-option" data-type="company">
                        <i class="fas fa-building"></i> Entreprise
                    </div>
                </div>
                
                <input type="hidden" name="user_type" id="user_type" value="candidate">
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                    <i class="fas fa-sign-in-alt"></i>
                    Se connecter
                </button>
            </form>
            
            <div class="forgot-password">
                <a href="forgot-password.php">Mot de passe oublié?</a>
            </div>
            
            <div style="text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                <p>Vous n'avez pas de compte? <a href="register-type.php" style="color: #2c5aa0;">S'inscrire</a></p>
                <p><a href="index.php" style="color: #666;">← Retour à l'accueil</a></p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userTypeOptions = document.querySelectorAll('.user-type-option');
            const userTypeInput = document.getElementById('user_type');
            
            userTypeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    userTypeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    
                    const type = this.dataset.type;
                    userTypeInput.value = type;
                });
            });
        });
    </script>
</body>
</html>
