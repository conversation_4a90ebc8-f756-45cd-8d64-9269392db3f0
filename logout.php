<?php
session_start();

// Store user type before destroying session
$user_type = $_SESSION['user_type'] ?? 'candidate';

// Destroy session
if (isset($_SESSION['logged_in'])) {
    // Clear all session variables
    $_SESSION = array();

    // Destroy the session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // Destroy the session
    session_destroy();
}

// Redirect based on user type
if ($user_type === 'company') {
    header('Location: login-company.php?message=logged_out');
} else {
    header('Location: login-candidate.php?message=logged_out');
}
exit;
?>
