<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'candidate') {
    header('Location: login-candidate.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Get user applications
$applications = $database->fetchAll("
    SELECT ja.*, j.title as job_title, j.slug as job_slug, j.location, j.job_type,
           c.name as company_name, c.logo as company_logo
    FROM job_applications ja
    JOIN jobs j ON ja.job_id = j.id
    JOIN companies c ON j.company_id = c.id
    WHERE ja.user_id = ?
    ORDER BY ja.applied_at DESC
", [$user_id]);

// Get statistics
$stats = [
    'total' => count($applications),
    'pending' => count(array_filter($applications, fn($app) => $app['status'] === 'pending')),
    'accepted' => count(array_filter($applications, fn($app) => $app['status'] === 'accepted')),
    'rejected' => count(array_filter($applications, fn($app) => $app['status'] === 'rejected'))
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Candidatures - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .applications-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }
        
        .stat-card.total i { color: #667eea; }
        .stat-card.pending i { color: #ffc107; }
        .stat-card.accepted i { color: #28a745; }
        .stat-card.rejected i { color: #dc3545; }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
            color: #333;
        }
        
        .applications-list {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .application-item {
            padding: 25px;
            border-bottom: 1px solid #e9ecef;
            transition: background 0.3s;
        }
        
        .application-item:hover {
            background: #f8f9fa;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .job-info {
            flex: 1;
        }
        
        .job-info h3 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.3rem;
        }
        
        .job-info h3 a {
            color: #667eea;
            text-decoration: none;
        }
        
        .job-info h3 a:hover {
            text-decoration: underline;
        }
        
        .company-name {
            color: #666;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .job-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .job-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-accepted {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .application-date {
            font-size: 0.9rem;
            color: #999;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .btn-back {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }
        
        .btn-back:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
    </style>
</head>
<body>
    <div class="applications-container">
        <div class="page-header">
            <div class="container">
                <h1><i class="fas fa-paper-plane"></i> Mes Candidatures</h1>
                <p>Suivez l'état de toutes vos candidatures</p>
            </div>
        </div>
        
        <div class="container">
            <a href="candidate-dashboard.php" class="btn-back">
                <i class="fas fa-arrow-left"></i> Retour au tableau de bord
            </a>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card total">
                    <i class="fas fa-paper-plane"></i>
                    <h3><?php echo $stats['total']; ?></h3>
                    <p>Total Candidatures</p>
                </div>
                
                <div class="stat-card pending">
                    <i class="fas fa-clock"></i>
                    <h3><?php echo $stats['pending']; ?></h3>
                    <p>En Attente</p>
                </div>
                
                <div class="stat-card accepted">
                    <i class="fas fa-check-circle"></i>
                    <h3><?php echo $stats['accepted']; ?></h3>
                    <p>Acceptées</p>
                </div>
                
                <div class="stat-card rejected">
                    <i class="fas fa-times-circle"></i>
                    <h3><?php echo $stats['rejected']; ?></h3>
                    <p>Rejetées</p>
                </div>
            </div>
            
            <!-- Applications List -->
            <div class="applications-list">
                <?php if (empty($applications)): ?>
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>Aucune candidature</h3>
                        <p>Vous n'avez pas encore postulé à des offres d'emploi.</p>
                        <a href="jobs.php" class="btn btn-primary" style="margin-top: 20px;">
                            <i class="fas fa-search"></i> Rechercher des emplois
                        </a>
                    </div>
                <?php else: ?>
                    <?php foreach ($applications as $app): ?>
                        <div class="application-item">
                            <div class="application-header">
                                <div class="job-info">
                                    <h3>
                                        <a href="emploi/<?php echo $app['job_slug']; ?>">
                                            <?php echo htmlspecialchars($app['job_title']); ?>
                                        </a>
                                    </h3>
                                    <div class="company-name">
                                        <?php echo htmlspecialchars($app['company_name']); ?>
                                    </div>
                                    <div class="job-meta">
                                        <span>
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($app['location']); ?>
                                        </span>
                                        <span>
                                            <i class="fas fa-clock"></i>
                                            <?php echo strtoupper($app['job_type']); ?>
                                        </span>
                                        <span>
                                            <i class="fas fa-calendar"></i>
                                            Candidature envoyée le <?php echo date('d/m/Y à H:i', strtotime($app['applied_at'])); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="status-badge status-<?php echo $app['status']; ?>">
                                    <?php 
                                    $status_text = [
                                        'pending' => 'En attente',
                                        'accepted' => 'Acceptée',
                                        'rejected' => 'Rejetée'
                                    ];
                                    echo $status_text[$app['status']];
                                    ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($app['cover_letter'])): ?>
                                <div class="cover-letter">
                                    <strong>Lettre de motivation:</strong>
                                    <p><?php echo nl2br(htmlspecialchars(substr($app['cover_letter'], 0, 200))); ?>
                                    <?php if (strlen($app['cover_letter']) > 200): ?>...<?php endif; ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
