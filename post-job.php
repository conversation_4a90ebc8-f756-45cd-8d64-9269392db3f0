<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if company is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'company') {
    header('Location: login-company.php');
    exit;
}

$company_id = $_SESSION['user_id'];
$company = $database->fetch("SELECT * FROM companies WHERE id = ?", [$company_id]);

if (!$company) {
    session_destroy();
    header('Location: login.php');
    exit;
}

$errors = [];
$success = '';

if ($_POST) {
    $title = sanitize($_POST['title'] ?? '');
    $category_id = (int)($_POST['category_id'] ?? 0);
    $description = sanitize($_POST['description'] ?? '');
    $requirements = sanitize($_POST['requirements'] ?? '');
    $benefits = sanitize($_POST['benefits'] ?? '');
    $salary_min = (float)($_POST['salary_min'] ?? 0);
    $salary_max = (float)($_POST['salary_max'] ?? 0);
    $job_type = sanitize($_POST['job_type'] ?? '');
    $experience_level = sanitize($_POST['experience_level'] ?? '');
    $education_level = sanitize($_POST['education_level'] ?? '');
    $location = sanitize($_POST['location'] ?? '');
    $remote_work = isset($_POST['remote_work']) ? 1 : 0;
    $application_deadline = sanitize($_POST['application_deadline'] ?? '');
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    
    // Validation
    if (empty($title)) $errors[] = 'Le titre du poste est requis';
    if ($category_id <= 0) $errors[] = 'La catégorie est requise';
    if (empty($description)) $errors[] = 'La description est requise';
    if (empty($job_type)) $errors[] = 'Le type de contrat est requis';
    if (empty($experience_level)) $errors[] = 'Le niveau d\'expérience est requis';
    if ($salary_min > 0 && $salary_max > 0 && $salary_min > $salary_max) {
        $errors[] = 'Le salaire minimum ne peut pas être supérieur au salaire maximum';
    }
    if (!empty($application_deadline) && strtotime($application_deadline) < time()) {
        $errors[] = 'La date limite de candidature doit être dans le futur';
    }
    
    if (empty($errors)) {
        $data = [
            'company_id' => $company_id,
            'category_id' => $category_id,
            'title' => $title,
            'description' => $description,
            'requirements' => $requirements,
            'benefits' => $benefits,
            'salary_min' => $salary_min > 0 ? $salary_min : null,
            'salary_max' => $salary_max > 0 ? $salary_max : null,
            'salary_currency' => 'TND',
            'job_type' => $job_type,
            'experience_level' => $experience_level,
            'education_level' => $education_level,
            'location' => $location,
            'remote_work' => $remote_work,
            'application_deadline' => !empty($application_deadline) ? $application_deadline : null,
            'is_featured' => $is_featured,
            'is_active' => 1
        ];
        
        try {
            // Use direct PDO to avoid database class issues
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            $sql = "INSERT INTO jobs (company_id, category_id, title, description, requirements, benefits, salary_min, salary_max, salary_currency, job_type, experience_level, education_level, location, remote_work, application_deadline, is_featured, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $company_id, $category_id, $title, $description, $requirements, $benefits,
                $salary_min > 0 ? $salary_min : null, $salary_max > 0 ? $salary_max : null,
                'TND', $job_type, $experience_level, $education_level, $location,
                $remote_work, !empty($application_deadline) ? $application_deadline : null,
                $is_featured, 1
            ]);

            if ($result) {
                $job_id = $pdo->lastInsertId();
                $success = 'Offre d\'emploi publiée avec succès!';
                // Clear form data
                $_POST = [];
            } else {
                $errors[] = 'Erreur lors de la publication de l\'offre';
            }
        } catch (Exception $e) {
            $errors[] = 'Erreur système: ' . $e->getMessage();
            error_log('Job posting error: ' . $e->getMessage());
        }
    }
}

// Get job categories
$categories = $database->fetchAll("SELECT * FROM job_categories WHERE is_active = 1 ORDER BY name");

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis',
    'ariana' => 'Ariana',
    'ben_arous' => 'Ben Arous',
    'manouba' => 'Manouba',
    'nabeul' => 'Nabeul',
    'zaghouan' => 'Zaghouan',
    'bizerte' => 'Bizerte',
    'beja' => 'Béja',
    'jendouba' => 'Jendouba',
    'kef' => 'Le Kef',
    'siliana' => 'Siliana',
    'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine',
    'sidi_bouzid' => 'Sidi Bouzid',
    'sousse' => 'Sousse',
    'monastir' => 'Monastir',
    'mahdia' => 'Mahdia',
    'sfax' => 'Sfax',
    'gabes' => 'Gabès',
    'medenine' => 'Médenine',
    'tataouine' => 'Tataouine',
    'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur',
    'kebili' => 'Kébili'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Publier une offre d'emploi - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .post-job-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .post-job-header {
            background: white;
            padding: 30px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .form-section:last-child {
            border-bottom: none;
        }
        
        .form-section h3 {
            color: #2c5aa0;
            margin-bottom: 20px;
            font-size: 1.3rem;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                padding: 20px;
                margin: 0 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="company-dashboard.php" class="nav-link">Tableau de bord</a></li>
                        <li><a href="post-job.php" class="nav-link active">Publier une offre</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <span style="margin-right: 15px;">Bonjour, <?php echo htmlspecialchars($company['name']); ?></span>
                        <a href="logout.php" class="btn btn-outline">Déconnexion</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="post-job-container">
        <div class="post-job-header">
            <div class="container">
                <h1 style="color: #2c5aa0; margin: 0;">Publier une nouvelle offre d'emploi</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Trouvez les meilleurs talents pour votre entreprise</p>
            </div>
        </div>

        <div class="container">
            <div class="form-container">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                        <br><br>
                        <a href="company-dashboard.php" class="btn btn-primary">Retour au tableau de bord</a>
                        <a href="post-job.php" class="btn btn-outline">Publier une autre offre</a>
                    </div>
                <?php endif; ?>

                <form method="POST" id="postJobForm">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> Informations de base</h3>
                        
                        <div class="form-group">
                            <label for="title">Titre du poste *</label>
                            <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="category_id">Catégorie *</label>
                                <select id="category_id" name="category_id" required>
                                    <option value="">Choisir une catégorie</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>" <?php echo ($_POST['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="job_type">Type de contrat *</label>
                                <select id="job_type" name="job_type" required>
                                    <option value="">Choisir le type</option>
                                    <option value="cdi" <?php echo ($_POST['job_type'] ?? '') === 'cdi' ? 'selected' : ''; ?>>CDI</option>
                                    <option value="cdd" <?php echo ($_POST['job_type'] ?? '') === 'cdd' ? 'selected' : ''; ?>>CDD</option>
                                    <option value="stage" <?php echo ($_POST['job_type'] ?? '') === 'stage' ? 'selected' : ''; ?>>Stage</option>
                                    <option value="freelance" <?php echo ($_POST['job_type'] ?? '') === 'freelance' ? 'selected' : ''; ?>>Freelance</option>
                                    <option value="temps_partiel" <?php echo ($_POST['job_type'] ?? '') === 'temps_partiel' ? 'selected' : ''; ?>>Temps Partiel</option>
                                    <option value="interim" <?php echo ($_POST['job_type'] ?? '') === 'interim' ? 'selected' : ''; ?>>Intérim</option>
                                    <option value="apprentissage" <?php echo ($_POST['job_type'] ?? '') === 'apprentissage' ? 'selected' : ''; ?>>Apprentissage</option>
                                    <option value="saisonnier" <?php echo ($_POST['job_type'] ?? '') === 'saisonnier' ? 'selected' : ''; ?>>Saisonnier</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="experience_level">Niveau d'expérience *</label>
                                <select id="experience_level" name="experience_level" required>
                                    <option value="">Choisir le niveau</option>
                                    <option value="entry" <?php echo ($_POST['experience_level'] ?? '') === 'entry' ? 'selected' : ''; ?>>Débutant</option>
                                    <option value="mid" <?php echo ($_POST['experience_level'] ?? '') === 'mid' ? 'selected' : ''; ?>>Intermédiaire</option>
                                    <option value="senior" <?php echo ($_POST['experience_level'] ?? '') === 'senior' ? 'selected' : ''; ?>>Senior</option>
                                    <option value="executive" <?php echo ($_POST['experience_level'] ?? '') === 'executive' ? 'selected' : ''; ?>>Cadre</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="education_level">Niveau d'éducation</label>
                                <select id="education_level" name="education_level">
                                    <option value="">Non spécifié</option>
                                    <option value="high_school" <?php echo ($_POST['education_level'] ?? '') === 'high_school' ? 'selected' : ''; ?>>Baccalauréat</option>
                                    <option value="bachelor" <?php echo ($_POST['education_level'] ?? '') === 'bachelor' ? 'selected' : ''; ?>>Licence</option>
                                    <option value="master" <?php echo ($_POST['education_level'] ?? '') === 'master' ? 'selected' : ''; ?>>Master</option>
                                    <option value="phd" <?php echo ($_POST['education_level'] ?? '') === 'phd' ? 'selected' : ''; ?>>Doctorat</option>
                                    <option value="other" <?php echo ($_POST['education_level'] ?? '') === 'other' ? 'selected' : ''; ?>>Autre</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Location -->
                    <div class="form-section">
                        <h3><i class="fas fa-map-marker-alt"></i> Localisation</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="location">Lieu *</label>
                                <input type="text" id="location" name="location" value="<?php echo htmlspecialchars($_POST['location'] ?? ''); ?>" placeholder="Ex: Tunis, Sfax, Sousse" required>
                            </div>
                        </div>
                        
                        <div class="checkbox-group">
                            <input type="checkbox" id="remote_work" name="remote_work" value="1" <?php echo isset($_POST['remote_work']) ? 'checked' : ''; ?>>
                            <label for="remote_work">Travail à distance possible</label>
                        </div>
                    </div>

                    <!-- Salary -->
                    <div class="form-section">
                        <h3><i class="fas fa-money-bill-wave"></i> Rémunération</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="salary_min">Salaire minimum (TND)</label>
                                <input type="number" id="salary_min" name="salary_min" value="<?php echo htmlspecialchars($_POST['salary_min'] ?? ''); ?>" min="0" step="50" placeholder="Ex: 1000">
                            </div>
                            
                            <div class="form-group">
                                <label for="salary_max">Salaire maximum (TND)</label>
                                <input type="number" id="salary_max" name="salary_max" value="<?php echo htmlspecialchars($_POST['salary_max'] ?? ''); ?>" min="0" step="50" placeholder="Ex: 1500">
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="form-section">
                        <h3><i class="fas fa-file-alt"></i> Description du poste</h3>
                        
                        <div class="form-group">
                            <label for="description">Description *</label>
                            <textarea id="description" name="description" required placeholder="Décrivez le poste, les responsabilités, l'environnement de travail..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="requirements">Exigences et qualifications</label>
                            <textarea id="requirements" name="requirements" placeholder="Compétences requises, expérience, diplômes..."><?php echo htmlspecialchars($_POST['requirements'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="benefits">Avantages</label>
                            <textarea id="benefits" name="benefits" placeholder="Avantages sociaux, formations, évolution de carrière..."><?php echo htmlspecialchars($_POST['benefits'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Additional Options -->
                    <div class="form-section">
                        <h3><i class="fas fa-cog"></i> Options</h3>
                        
                        <div class="form-group">
                            <label for="application_deadline">Date limite de candidature</label>
                            <input type="date" id="application_deadline" name="application_deadline" value="<?php echo htmlspecialchars($_POST['application_deadline'] ?? ''); ?>" min="<?php echo date('Y-m-d', strtotime('+1 day')); ?>">
                        </div>
                        
                        <div class="checkbox-group">
                            <input type="checkbox" id="is_featured" name="is_featured" value="1" <?php echo isset($_POST['is_featured']) ? 'checked' : ''; ?>>
                            <label for="is_featured">Offre mise en avant (recommandé)</label>
                        </div>
                    </div>

                    <div style="text-align: center; padding-top: 20px;">
                        <button type="submit" class="btn btn-primary" style="padding: 15px 40px; font-size: 1.1rem;">
                            <i class="fas fa-paper-plane"></i>
                            Publier l'offre d'emploi
                        </button>
                        <a href="company-dashboard.php" class="btn btn-outline" style="margin-left: 20px;">
                            Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
