<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'candidate') {
    header('Location: login-candidate.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user = $database->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    header('Location: login.php');
    exit;
}

$errors = [];
$success = '';

if ($_POST) {
    $first_name = sanitize($_POST['first_name'] ?? '');
    $last_name = sanitize($_POST['last_name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $date_of_birth = sanitize($_POST['date_of_birth'] ?? '');
    $gender = sanitize($_POST['gender'] ?? '');
    $address = sanitize($_POST['address'] ?? '');
    $city = sanitize($_POST['city'] ?? '');
    $governorate = sanitize($_POST['governorate'] ?? '');
    $skills = sanitize($_POST['skills'] ?? '');
    $experience_years = (int)($_POST['experience_years'] ?? 0);
    $education_level = sanitize($_POST['education_level'] ?? '');
    
    // Validation
    if (empty($first_name)) $errors[] = 'Le prénom est requis';
    if (empty($last_name)) $errors[] = 'Le nom est requis';
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    if (empty($phone)) $errors[] = 'Le numéro de téléphone est requis';
    
    // Check if email is already used by another user
    if (empty($errors)) {
        $existing = $database->fetch("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user_id]);
        if ($existing) {
            $errors[] = 'Cette adresse email est déjà utilisée par un autre utilisateur';
        }
    }
    
    // Handle CV upload
    $cv_file = $user['cv_file']; // Keep existing CV by default
    if (isset($_FILES['cv_file']) && $_FILES['cv_file']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/cvs/';
        $file_extension = strtolower(pathinfo($_FILES['cv_file']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['pdf', 'doc', 'docx'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            if ($_FILES['cv_file']['size'] <= MAX_FILE_SIZE) {
                $new_cv_file = uniqid() . '_' . $user_id . '.' . $file_extension;
                $upload_path = $upload_dir . $new_cv_file;
                
                if (move_uploaded_file($_FILES['cv_file']['tmp_name'], $upload_path)) {
                    // Delete old CV file if exists
                    if ($cv_file && file_exists($upload_dir . $cv_file)) {
                        unlink($upload_dir . $cv_file);
                    }
                    $cv_file = $new_cv_file;
                } else {
                    $errors[] = 'Erreur lors du téléchargement du CV';
                }
            } else {
                $errors[] = 'Le fichier CV est trop volumineux (max 5MB)';
            }
        } else {
            $errors[] = 'Format de fichier non autorisé (PDF, DOC, DOCX uniquement)';
        }
    }
    
    // Handle profile image upload
    $profile_image = $user['profile_image']; // Keep existing image by default
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/profiles/';
        $file_extension = strtolower(pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            if ($_FILES['profile_image']['size'] <= MAX_FILE_SIZE) {
                $new_profile_image = uniqid() . '_' . $user_id . '.' . $file_extension;
                $upload_path = $upload_dir . $new_profile_image;
                
                if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                    // Delete old profile image if exists
                    if ($profile_image && file_exists($upload_dir . $profile_image)) {
                        unlink($upload_dir . $profile_image);
                    }
                    $profile_image = $new_profile_image;
                } else {
                    $errors[] = 'Erreur lors du téléchargement de la photo de profil';
                }
            } else {
                $errors[] = 'La photo de profil est trop volumineuse (max 5MB)';
            }
        } else {
            $errors[] = 'Format d\'image non autorisé (JPG, PNG, GIF uniquement)';
        }
    }
    
    if (empty($errors)) {
        $data = [
            'first_name' => $first_name,
            'last_name' => $last_name,
            'email' => $email,
            'phone' => $phone,
            'date_of_birth' => !empty($date_of_birth) ? $date_of_birth : null,
            'gender' => $gender,
            'address' => $address,
            'city' => $city,
            'governorate' => $governorate,
            'skills' => $skills,
            'experience_years' => $experience_years,
            'education_level' => $education_level,
            'cv_file' => $cv_file,
            'profile_image' => $profile_image
        ];
        
        $result = $database->update('users', $data, 'id = :id', ['id' => $user_id]);
        if ($result) {
            $success = 'Profil mis à jour avec succès!';
            // Update session name if changed
            $_SESSION['user_name'] = $first_name . ' ' . $last_name;
            // Refresh user data
            $user = $database->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);
        } else {
            $errors[] = 'Erreur lors de la mise à jour du profil';
        }
    }
}

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis', 'ariana' => 'Ariana', 'ben_arous' => 'Ben Arous', 'manouba' => 'Manouba',
    'nabeul' => 'Nabeul', 'zaghouan' => 'Zaghouan', 'bizerte' => 'Bizerte', 'beja' => 'Béja',
    'jendouba' => 'Jendouba', 'kef' => 'Le Kef', 'siliana' => 'Siliana', 'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine', 'sidi_bouzid' => 'Sidi Bouzid', 'sousse' => 'Sousse',
    'monastir' => 'Monastir', 'mahdia' => 'Mahdia', 'sfax' => 'Sfax', 'gabes' => 'Gabès',
    'medenine' => 'Médenine', 'tataouine' => 'Tataouine', 'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur', 'kebili' => 'Kébili'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Profil - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .profile-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .profile-header {
            background: white;
            padding: 30px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .profile-image-section {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #2c5aa0;
            margin-bottom: 20px;
        }
        
        .profile-image-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            border: 4px solid #e9ecef;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 0.9rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                padding: 20px;
                margin: 0 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="jobs.php" class="nav-link">Emplois</a></li>
                        <li><a href="user-dashboard.php" class="nav-link">Tableau de bord</a></li>
                        <li><a href="profile.php" class="nav-link active">Mon Profil</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <span style="margin-right: 15px;">Bonjour, <?php echo htmlspecialchars($user['first_name']); ?></span>
                        <a href="logout.php" class="btn btn-outline">Déconnexion</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="profile-container">
        <div class="profile-header">
            <div class="container">
                <h1 style="color: #2c5aa0; margin: 0;">Mon Profil</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Gérez vos informations personnelles et professionnelles</p>
            </div>
        </div>

        <div class="container">
            <div class="form-container">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-error">
                        <ul style="margin: 0; padding-left: 20px;">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data">
                    <!-- Profile Image Section -->
                    <div class="profile-image-section">
                        <?php if ($user['profile_image']): ?>
                            <img src="uploads/profiles/<?php echo $user['profile_image']; ?>" alt="Photo de profil" class="profile-image">
                        <?php else: ?>
                            <div class="profile-image-placeholder">
                                <i class="fas fa-user" style="font-size: 3rem; color: #ccc;"></i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="form-group" style="max-width: 300px; margin: 0 auto;">
                            <label for="profile_image">Photo de profil</label>
                            <input type="file" id="profile_image" name="profile_image" accept=".jpg,.jpeg,.png,.gif">
                            <small style="color: #666;">JPG, PNG, GIF - Max 5MB</small>
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <h3 style="color: #2c5aa0; margin-bottom: 20px;"><i class="fas fa-user"></i> Informations personnelles</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name">Prénom *</label>
                            <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="last_name">Nom *</label>
                            <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">Téléphone *</label>
                            <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone']); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_of_birth">Date de naissance</label>
                            <input type="date" id="date_of_birth" name="date_of_birth" value="<?php echo htmlspecialchars($user['date_of_birth']); ?>">
                        </div>
                        <div class="form-group">
                            <label for="gender">Genre</label>
                            <select id="gender" name="gender">
                                <option value="">Non spécifié</option>
                                <option value="male" <?php echo $user['gender'] === 'male' ? 'selected' : ''; ?>>Homme</option>
                                <option value="female" <?php echo $user['gender'] === 'female' ? 'selected' : ''; ?>>Femme</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="address">Adresse</label>
                        <textarea id="address" name="address" placeholder="Votre adresse complète"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="city">Ville</label>
                            <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($user['city']); ?>" placeholder="Ex: Tunis">
                        </div>
                        <div class="form-group">
                            <label for="governorate">Gouvernorat</label>
                            <select id="governorate" name="governorate">
                                <option value="">Choisir le gouvernorat</option>
                                <?php foreach ($governorates as $key => $name): ?>
                                    <option value="<?php echo $key; ?>" <?php echo $user['governorate'] === $key ? 'selected' : ''; ?>>
                                        <?php echo $name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Professional Information -->
                    <h3 style="color: #2c5aa0; margin: 30px 0 20px 0;"><i class="fas fa-briefcase"></i> Informations professionnelles</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="experience_years">Années d'expérience</label>
                            <input type="number" id="experience_years" name="experience_years" value="<?php echo htmlspecialchars($user['experience_years']); ?>" min="0" max="50">
                        </div>
                        <div class="form-group">
                            <label for="education_level">Niveau d'éducation</label>
                            <select id="education_level" name="education_level">
                                <option value="">Non spécifié</option>
                                <option value="high_school" <?php echo $user['education_level'] === 'high_school' ? 'selected' : ''; ?>>Baccalauréat</option>
                                <option value="bachelor" <?php echo $user['education_level'] === 'bachelor' ? 'selected' : ''; ?>>Licence</option>
                                <option value="master" <?php echo $user['education_level'] === 'master' ? 'selected' : ''; ?>>Master</option>
                                <option value="phd" <?php echo $user['education_level'] === 'phd' ? 'selected' : ''; ?>>Doctorat</option>
                                <option value="other" <?php echo $user['education_level'] === 'other' ? 'selected' : ''; ?>>Autre</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="skills">Compétences</label>
                        <textarea id="skills" name="skills" placeholder="Listez vos compétences séparées par des virgules (ex: PHP, JavaScript, Marketing Digital)"><?php echo htmlspecialchars($user['skills'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="cv_file">CV (PDF, DOC, DOCX - Max 5MB)</label>
                        <input type="file" id="cv_file" name="cv_file" accept=".pdf,.doc,.docx">
                        <?php if ($user['cv_file']): ?>
                            <div class="file-info">
                                <i class="fas fa-file-pdf"></i>
                                CV actuel: <?php echo htmlspecialchars($user['cv_file']); ?>
                                <a href="uploads/cvs/<?php echo $user['cv_file']; ?>" target="_blank" style="margin-left: 10px;">
                                    <i class="fas fa-download"></i> Télécharger
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div style="text-align: center; padding-top: 30px;">
                        <button type="submit" class="btn btn-primary" style="padding: 15px 40px; font-size: 1.1rem;">
                            <i class="fas fa-save"></i>
                            Mettre à jour le profil
                        </button>
                        <a href="user-dashboard.php" class="btn btn-outline" style="margin-left: 20px;">
                            Retour au tableau de bord
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
