<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = '';

if ($_POST) {
    $company_name = sanitize($_POST['company_name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $phone = sanitize($_POST['phone'] ?? '');
    $website = sanitize($_POST['website'] ?? '');
    $industry = sanitize($_POST['industry'] ?? '');
    $employee_count = sanitize($_POST['employee_count'] ?? '');
    $governorate = sanitize($_POST['governorate'] ?? '');
    
    // Validation
    if (empty($company_name)) $errors[] = 'Le nom de l\'entreprise est requis';
    if (empty($email)) $errors[] = 'L\'email est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    if (empty($password)) $errors[] = 'Le mot de passe est requis';
    if (strlen($password) < 6) $errors[] = 'Le mot de passe doit contenir au moins 6 caractères';
    if ($password !== $confirm_password) $errors[] = 'Les mots de passe ne correspondent pas';
    if (empty($phone)) $errors[] = 'Le numéro de téléphone est requis';
    if (empty($governorate)) $errors[] = 'Le gouvernorat est requis';
    if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
        $errors[] = 'Format de site web invalide';
    }
    
    // Check if email already exists
    if (empty($errors)) {
        $existing = $database->fetch("SELECT id FROM companies WHERE email = ?", [$email]);
        if ($existing) {
            $errors[] = 'Cette adresse email est déjà utilisée';
        }
    }
    
    // Insert company
    if (empty($errors)) {
        try {
            $hashedPassword = hashPassword($password);
            $verificationToken = generateToken();

            // Use direct PDO instead of database class to avoid issues
            $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            $sql = "INSERT INTO companies (name, email, password, phone, website, industry, employee_count, governorate, verification_token, is_active, is_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, 0)";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$company_name, $email, $hashedPassword, $phone, $website, $industry, $employee_count, $governorate, $verificationToken]);

            if ($result) {
                $success = 'Compte entreprise créé avec succès! Votre compte est en attente d\'approbation par l\'administrateur.';
            } else {
                $errors[] = 'Erreur lors de la création du compte. Veuillez réessayer.';
            }
        } catch (Exception $e) {
            $errors[] = 'Erreur système: ' . $e->getMessage();
            error_log('Company registration error: ' . $e->getMessage());
        }
    }
}

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis', 'ariana' => 'Ariana', 'ben_arous' => 'Ben Arous', 'manouba' => 'Manouba',
    'nabeul' => 'Nabeul', 'zaghouan' => 'Zaghouan', 'bizerte' => 'Bizerte', 'beja' => 'Béja',
    'jendouba' => 'Jendouba', 'kef' => 'Le Kef', 'siliana' => 'Siliana', 'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine', 'sidi_bouzid' => 'Sidi Bouzid', 'sousse' => 'Sousse',
    'monastir' => 'Monastir', 'mahdia' => 'Mahdia', 'sfax' => 'Sfax', 'gabes' => 'Gabès',
    'medenine' => 'Médenine', 'tataouine' => 'Tataouine', 'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur', 'kebili' => 'Kébili'
];

$industries = [
    'Technology' => 'Technologie',
    'Finance' => 'Finance',
    'Healthcare' => 'Santé',
    'Education' => 'Éducation',
    'Manufacturing' => 'Industrie',
    'Retail' => 'Commerce de détail',
    'Construction' => 'Construction',
    'Transportation' => 'Transport',
    'Tourism' => 'Tourisme',
    'Agriculture' => 'Agriculture',
    'Energy' => 'Énergie',
    'Telecommunications' => 'Télécommunications',
    'Media' => 'Médias',
    'Consulting' => 'Conseil',
    'Other' => 'Autre'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription Entreprise - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            padding: 20px;
        }
        
        .register-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 700px;
            width: 100%;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-header h1 {
            color: #2c5aa0;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .register-header .user-type {
            background: #e8f4fd;
            color: #2c5aa0;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-form">
            <div class="register-header">
                <h1><i class="fas fa-briefcase"></i> Concours Tunisie</h1>
                <div class="user-type">
                    <i class="fas fa-building"></i>
                    <span>Inscription Entreprise</span>
                </div>
                <p>Créez votre compte entreprise pour recruter des talents</p>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                    <br><br>
                    <a href="login.php" class="btn btn-primary">Se connecter</a>
                    <a href="index.php" class="btn btn-outline">Retour à l'accueil</a>
                </div>
            <?php else: ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="company_name">Nom de l'entreprise *</label>
                    <input type="text" id="company_name" name="company_name" value="<?php echo htmlspecialchars($_POST['company_name'] ?? ''); ?>" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">Téléphone *</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Mot de passe *</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Confirmer le mot de passe *</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="website">Site web</label>
                        <input type="url" id="website" name="website" value="<?php echo htmlspecialchars($_POST['website'] ?? ''); ?>" placeholder="https://www.exemple.com">
                    </div>
                    <div class="form-group">
                        <label for="industry">Secteur d'activité</label>
                        <select id="industry" name="industry">
                            <option value="">Choisir le secteur</option>
                            <?php foreach ($industries as $key => $name): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($_POST['industry'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo $name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="employee_count">Taille de l'entreprise</label>
                        <select id="employee_count" name="employee_count">
                            <option value="">Choisir la taille</option>
                            <option value="1-10" <?php echo ($_POST['employee_count'] ?? '') === '1-10' ? 'selected' : ''; ?>>1-10 employés</option>
                            <option value="11-50" <?php echo ($_POST['employee_count'] ?? '') === '11-50' ? 'selected' : ''; ?>>11-50 employés</option>
                            <option value="51-200" <?php echo ($_POST['employee_count'] ?? '') === '51-200' ? 'selected' : ''; ?>>51-200 employés</option>
                            <option value="201-500" <?php echo ($_POST['employee_count'] ?? '') === '201-500' ? 'selected' : ''; ?>>201-500 employés</option>
                            <option value="501-1000" <?php echo ($_POST['employee_count'] ?? '') === '501-1000' ? 'selected' : ''; ?>>501-1000 employés</option>
                            <option value="1000+" <?php echo ($_POST['employee_count'] ?? '') === '1000+' ? 'selected' : ''; ?>>1000+ employés</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="governorate">Gouvernorat *</label>
                        <select id="governorate" name="governorate" required>
                            <option value="">Choisir le gouvernorat</option>
                            <?php foreach ($governorates as $key => $name): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($_POST['governorate'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo $name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                    <i class="fas fa-building"></i>
                    Créer mon compte entreprise
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 20px;">
                <p>Vous avez déjà un compte? <a href="login.php" style="color: #2c5aa0;">Se connecter</a></p>
                <p><a href="register-type.php" style="color: #666;">← Changer le type de compte</a></p>
            </div>
            
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
