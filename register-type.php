<?php
require_once 'includes/seo.php';

// Generate SEO data for registration page
$seo_data = [
    'title' => 'Inscription - tunisieconcours.org Offres d\'Emploi',
    'description' => 'Créez votre compte sur tunisieconcours.org. Inscription gratuite pour candidats et entreprises. Accédez aux meilleures offres d\'emploi en Tunisie.',
    'keywords' => 'inscription, créer compte, emploi tunisie, registration, tunisieconcours, candidat, entreprise',
    'canonical' => 'https://tunisieconcours.org/register-type.php',
    'og_title' => 'Inscription Gratuite - Emploi Tunisie',
    'og_description' => 'Rejoignez la plateforme leader d\'emploi en Tunisie. Inscription gratuite pour candidats et entreprises.',
    'og_url' => 'https://tunisieconcours.org/register-type.php',
    'og_image' => 'https://tunisieconcours.org/assets/images/logo-og.jpg'
];
?>
<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php $seo->renderMetaTags($seo_data); ?>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .register-type-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            padding: 20px;
        }
        
        .register-type-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .register-type-header {
            margin-bottom: 40px;
        }
        
        .register-type-header h1 {
            color: #2c5aa0;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .register-type-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .type-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .type-option {
            padding: 40px 30px;
            border: 3px solid #e9ecef;
            border-radius: 15px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .type-option:hover {
            border-color: #2c5aa0;
            background: #f8f9ff;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(44, 90, 160, 0.1);
            text-decoration: none;
            color: #2c5aa0;
        }
        
        .type-option i {
            font-size: 4rem;
            color: #2c5aa0;
            margin-bottom: 20px;
            display: block;
        }
        
        .type-option h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: inherit;
        }
        
        .type-option p {
            color: #666;
            line-height: 1.6;
            margin: 0;
        }
        
        .type-option ul {
            list-style: none;
            padding: 0;
            margin: 15px 0 0 0;
            text-align: left;
        }
        
        .type-option li {
            padding: 5px 0;
            color: #666;
        }
        
        .type-option li i {
            font-size: 1rem;
            color: #28a745;
            margin-right: 10px;
            width: auto;
            display: inline;
        }
        
        .back-link {
            margin-top: 30px;
        }
        
        .back-link a {
            color: #666;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .back-link a:hover {
            color: #2c5aa0;
        }
        
        @media (max-width: 768px) {
            .type-options {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .type-option {
                padding: 30px 20px;
            }
            
            .type-option i {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-type-container">
        <div class="register-type-form">
            <div class="register-type-header">
                <h1><i class="fas fa-briefcase"></i> Concours Tunisie</h1>
                <p>Choisissez le type de compte que vous souhaitez créer</p>
            </div>
            
            <div class="type-options">
                <a href="register-candidate.php" class="type-option">
                    <i class="fas fa-user"></i>
                    <h3>Candidat</h3>
                    <p>Je cherche un emploi</p>
                    <ul>
                        <li><i class="fas fa-check"></i> Rechercher des emplois</li>
                        <li><i class="fas fa-check"></i> Postuler aux offres</li>
                        <li><i class="fas fa-check"></i> Gérer mon CV</li>
                        <li><i class="fas fa-check"></i> Suivre mes candidatures</li>
                    </ul>
                </a>
                
                <a href="register-company.php" class="type-option">
                    <i class="fas fa-building"></i>
                    <h3>Entreprise</h3>
                    <p>Je recrute des talents</p>
                    <ul>
                        <li><i class="fas fa-check"></i> Publier des offres d'emploi</li>
                        <li><i class="fas fa-check"></i> Gérer les candidatures</li>
                        <li><i class="fas fa-check"></i> Rechercher des profils</li>
                        <li><i class="fas fa-check"></i> Promouvoir mon entreprise</li>
                    </ul>
                </a>
            </div>
            
            <div class="back-link">
                <a href="index.php">
                    <i class="fas fa-arrow-left"></i>
                    Retour à l'accueil
                </a>
            </div>
        </div>
    </div>
</body>
</html>
