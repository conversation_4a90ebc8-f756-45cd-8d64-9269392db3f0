<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

$errors = [];
$success = '';

if ($_POST) {
    $first_name = sanitize($_POST['first_name'] ?? '');
    $last_name = sanitize($_POST['last_name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $phone = sanitize($_POST['phone'] ?? '');
    $governorate = sanitize($_POST['governorate'] ?? '');
    $user_type = sanitize($_POST['user_type'] ?? 'candidate');
    
    // Validation
    if ($user_type === 'candidate') {
        if (empty($first_name)) $errors[] = 'Le prénom est requis';
        if (empty($last_name)) $errors[] = 'Le nom est requis';
    } else {
        $company_name = sanitize($_POST['company_name'] ?? '');
        if (empty($company_name)) $errors[] = 'Le nom de l\'entreprise est requis';
    }

    if (empty($email)) $errors[] = 'L\'email est requis';
    if (!validateEmail($email)) $errors[] = 'Format d\'email invalide';
    if (empty($password)) $errors[] = 'Le mot de passe est requis';
    if (strlen($password) < 6) $errors[] = 'Le mot de passe doit contenir au moins 6 caractères';
    if ($password !== $confirm_password) $errors[] = 'Les mots de passe ne correspondent pas';
    if (empty($phone)) $errors[] = 'Le numéro de téléphone est requis';
    if (empty($governorate)) $errors[] = 'Le gouvernorat est requis';
    
    // Check if email already exists
    if (empty($errors)) {
        $table = ($user_type === 'company') ? 'companies' : 'users';
        $existing = $database->fetch("SELECT id FROM {$table} WHERE email = ?", [$email]);
        if ($existing) {
            $errors[] = 'Cette adresse email est déjà utilisée';
        }
    }
    
    // Insert user
    if (empty($errors)) {
        $hashedPassword = hashPassword($password);
        $verificationToken = generateToken();
        
        if ($user_type === 'company') {
            if (empty($errors)) {
                $data = [
                    'name' => $company_name,
                    'email' => $email,
                    'password' => $hashedPassword,
                    'phone' => $phone,
                    'governorate' => $governorate,
                    'verification_token' => $verificationToken,
                    'is_active' => 1,
                    'is_verified' => 0
                ];
                
                $result = $database->insert('companies', $data);
                if ($result) {
                    $success = 'Compte entreprise créé avec succès! Vous pouvez maintenant vous connecter.';
                } else {
                    $errors[] = 'Erreur lors de la création du compte';
                }
            }
        } else {
            $data = [
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email,
                'password' => $hashedPassword,
                'phone' => $phone,
                'governorate' => $governorate,
                'verification_token' => $verificationToken,
                'is_active' => 1,
                'email_verified' => 0
            ];
            
            $result = $database->insert('users', $data);
            if ($result) {
                $success = 'Compte créé avec succès! Vous pouvez maintenant vous connecter.';
            } else {
                $errors[] = 'Erreur lors de la création du compte';
            }
        }
    }
}

// Tunisian governorates
$governorates = [
    'tunis' => 'Tunis',
    'ariana' => 'Ariana',
    'ben_arous' => 'Ben Arous',
    'manouba' => 'Manouba',
    'nabeul' => 'Nabeul',
    'zaghouan' => 'Zaghouan',
    'bizerte' => 'Bizerte',
    'beja' => 'Béja',
    'jendouba' => 'Jendouba',
    'kef' => 'Le Kef',
    'siliana' => 'Siliana',
    'kairouan' => 'Kairouan',
    'kasserine' => 'Kasserine',
    'sidi_bouzid' => 'Sidi Bouzid',
    'sousse' => 'Sousse',
    'monastir' => 'Monastir',
    'mahdia' => 'Mahdia',
    'sfax' => 'Sfax',
    'gabes' => 'Gabès',
    'medenine' => 'Médenine',
    'tataouine' => 'Tataouine',
    'gafsa' => 'Gafsa',
    'tozeur' => 'Tozeur',
    'kebili' => 'Kébili'
];
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            padding: 20px;
        }
        
        .register-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .register-header h1 {
            color: #2c5aa0;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .user-type-selector {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .user-type-option {
            flex: 1;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .user-type-option.active {
            border-color: #2c5aa0;
            background: #f8f9ff;
        }
        
        .user-type-option i {
            font-size: 2rem;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .user-type-selector {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-form">
            <div class="register-header">
                <h1><i class="fas fa-briefcase"></i> Concours Tunisie</h1>
                <p>Créer votre compte</p>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                    <br><br>
                    <a href="login.php" class="btn btn-primary">Se connecter maintenant</a>
                </div>
            <?php else: ?>
            
            <form method="POST" id="registerForm">
                <div class="user-type-selector">
                    <div class="user-type-option active" data-type="candidate">
                        <i class="fas fa-user"></i>
                        <h3>Candidat</h3>
                        <p>Je cherche un emploi</p>
                    </div>
                    <div class="user-type-option" data-type="company">
                        <i class="fas fa-building"></i>
                        <h3>Entreprise</h3>
                        <p>Je recrute des talents</p>
                    </div>
                </div>
                
                <input type="hidden" name="user_type" id="user_type" value="candidate">
                
                <div id="candidate_fields">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name">Prénom *</label>
                            <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="last_name">Nom *</label>
                            <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                        </div>
                    </div>
                </div>
                
                <div id="company_fields" style="display: none;">
                    <div class="form-group">
                        <label for="company_name">Nom de l'entreprise *</label>
                        <input type="text" id="company_name" name="company_name" value="<?php echo htmlspecialchars($_POST['company_name'] ?? ''); ?>" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Mot de passe *</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Confirmer le mot de passe *</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Téléphone *</label>
                        <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="governorate">Gouvernorat *</label>
                        <select id="governorate" name="governorate" required>
                            <option value="">Choisir le gouvernorat</option>
                            <?php foreach ($governorates as $key => $name): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($_POST['governorate'] ?? '') === $key ? 'selected' : ''; ?>>
                                    <?php echo $name; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%; margin-top: 20px;">
                    <i class="fas fa-user-plus"></i>
                    Créer mon compte
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 20px;">
                <p>Vous avez déjà un compte? <a href="login.php" style="color: #2c5aa0;">Se connecter</a></p>
            </div>
            
            <?php endif; ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const userTypeOptions = document.querySelectorAll('.user-type-option');
            const userTypeInput = document.getElementById('user_type');
            const candidateFields = document.getElementById('candidate_fields');
            const companyFields = document.getElementById('company_fields');
            
            // Initialize the form based on default selection
            function updateFormFields(type) {
                if (type === 'company') {
                    candidateFields.style.display = 'none';
                    companyFields.style.display = 'block';
                    document.getElementById('first_name').required = false;
                    document.getElementById('last_name').required = false;
                    document.getElementById('company_name').required = true;
                } else {
                    candidateFields.style.display = 'block';
                    companyFields.style.display = 'none';
                    document.getElementById('first_name').required = true;
                    document.getElementById('last_name').required = true;
                    document.getElementById('company_name').required = false;
                }
            }

            // Initialize on page load
            updateFormFields(userTypeInput.value);

            userTypeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    userTypeOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');

                    const type = this.dataset.type;
                    userTypeInput.value = type;
                    updateFormFields(type);
                });
            });
        });
    </script>
</body>
</html>
