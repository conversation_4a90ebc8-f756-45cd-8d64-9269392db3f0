<?php
// Simple PHP router for clean URLs
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove leading slash
$path = ltrim($path, '/');

// Handle job URLs: /job/slug
if (preg_match('#^job/([a-zA-Z0-9\-]+)/?$#', $path, $matches)) {
    $slug = $matches[1];
    $_GET['slug'] = $slug;
    include 'job.php';
    exit;
}

// Handle old job-details.php redirects
if ($path === 'job-details.php' && isset($_GET['id'])) {
    $job_id = (int)$_GET['id'];
    if ($job_id > 0) {
        require_once 'config/database.php';
        $job = $database->fetch("SELECT slug FROM jobs WHERE id = ? AND is_active = 1", [$job_id]);
        if ($job && !empty($job['slug'])) {
            header('Location: /job/' . $job['slug'], true, 301);
            exit;
        }
    }
    header('Location: jobs.php');
    exit;
}

// If no route matches, return false to continue normal processing
return false;
?>
