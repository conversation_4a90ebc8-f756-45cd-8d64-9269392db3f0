<?php
/**
 * Setup Script for Concours Tunisie Job Platform
 * This script helps initialize the database and create necessary directories
 */

// Check if setup is already completed
if (file_exists('config/setup_completed.txt')) {
    die('Setup has already been completed. Delete config/setup_completed.txt to run setup again.');
}

$errors = [];
$success = [];

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    $errors[] = 'PHP 7.4.0 or higher is required. Current version: ' . PHP_VERSION;
}

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'fileinfo'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $errors[] = "Required PHP extension '{$ext}' is not loaded.";
    }
}

// Handle form submission
if ($_POST) {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'concours_tunisie';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    
    try {
        // Test database connection
        $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$db_name}`");
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        if ($schema) {
            // Remove comments and split by semicolon
            $schema = preg_replace('/--.*$/m', '', $schema); // Remove single line comments
            $schema = preg_replace('/\/\*.*?\*\//s', '', $schema); // Remove multi-line comments

            $statements = array_filter(array_map('trim', explode(';', $schema)));
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                    } catch (PDOException $e) {
                        // Log the error but continue with other statements
                        error_log("SQL Error: " . $e->getMessage() . " - Statement: " . substr($statement, 0, 100));
                    }
                }
            }
            $success[] = 'Database schema created successfully.';
        }
        
        // Insert sample data if requested
        if (isset($_POST['sample_data'])) {
            $sample_data = file_get_contents('database/sample_data.sql');
            if ($sample_data) {
                $statements = array_filter(array_map('trim', explode(';', $sample_data)));
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^--/', $statement)) {
                        $pdo->exec($statement);
                    }
                }
                $success[] = 'Sample data inserted successfully.';
            }
        }
        
        // Update database configuration
        $config_content = file_get_contents('config/database.php');
        $config_content = str_replace("define('DB_HOST', 'localhost');", "define('DB_HOST', '{$db_host}');", $config_content);
        $config_content = str_replace("define('DB_NAME', 'concours_tunisie');", "define('DB_NAME', '{$db_name}');", $config_content);
        $config_content = str_replace("define('DB_USER', 'root');", "define('DB_USER', '{$db_user}');", $config_content);
        $config_content = str_replace("define('DB_PASS', '');", "define('DB_PASS', '{$db_pass}');", $config_content);
        
        file_put_contents('config/database.php', $config_content);
        $success[] = 'Database configuration updated.';
        
        // Create necessary directories
        $directories = [
            'uploads',
            'uploads/cvs',
            'uploads/logos',
            'uploads/profiles',
            'logs'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
                $success[] = "Created directory: {$dir}";
            }
        }
        
        // Create .htaccess for uploads security
        $htaccess_content = "Options -Indexes\n";
        $htaccess_content .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
        $htaccess_content .= "    deny from all\n";
        $htaccess_content .= "</Files>\n";
        
        file_put_contents('uploads/.htaccess', $htaccess_content);
        $success[] = 'Security .htaccess created for uploads directory.';
        
        // Mark setup as completed
        file_put_contents('config/setup_completed.txt', date('Y-m-d H:i:s'));
        $success[] = 'Setup completed successfully!';
        
    } catch (PDOException $e) {
        $errors[] = 'Database error: ' . $e->getMessage();
    } catch (Exception $e) {
        $errors[] = 'Error: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد منصة التوظيف التونسية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .setup-header h1 {
            color: #2c5aa0;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .setup-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2c5aa0;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            background: #2c5aa0;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #1e3f73;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .requirements {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .requirements h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
        }
        
        .requirements ul {
            list-style: none;
            padding: 0;
        }
        
        .requirements li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .requirements .check {
            color: #28a745;
        }
        
        .requirements .times {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-briefcase"></i> Concours Tunisie</h1>
            <p>إعداد منصة التوظيف التونسية</p>
        </div>
        
        <!-- System Requirements Check -->
        <div class="requirements">
            <h3>فحص متطلبات النظام</h3>
            <ul>
                <li>
                    <i class="fas <?php echo version_compare(PHP_VERSION, '7.4.0') >= 0 ? 'fa-check check' : 'fa-times times'; ?>"></i>
                    PHP 7.4.0+ (الحالي: <?php echo PHP_VERSION; ?>)
                </li>
                <?php foreach ($required_extensions as $ext): ?>
                <li>
                    <i class="fas <?php echo extension_loaded($ext) ? 'fa-check check' : 'fa-times times'; ?>"></i>
                    PHP Extension: <?php echo $ext; ?>
                </li>
                <?php endforeach; ?>
                <li>
                    <i class="fas <?php echo is_writable('.') ? 'fa-check check' : 'fa-times times'; ?>"></i>
                    صلاحيات الكتابة في المجلد الرئيسي
                </li>
            </ul>
        </div>
        
        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <strong>أخطاء:</strong>
                <ul style="margin: 10px 0 0 20px;">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <!-- Success Messages -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <strong>نجح:</strong>
                <ul style="margin: 10px 0 0 20px;">
                    <?php foreach ($success as $msg): ?>
                        <li><?php echo htmlspecialchars($msg); ?></li>
                    <?php endforeach; ?>
                </ul>
                
                <?php if (in_array('Setup completed successfully!', $success)): ?>
                    <div style="margin-top: 20px; text-align: center;">
                        <a href="index.php" class="btn" style="display: inline-block; text-decoration: none;">
                            انتقل إلى الموقع
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Setup Form -->
        <?php if (empty($success) || !in_array('Setup completed successfully!', $success)): ?>
        <form method="POST">
            <div class="form-group">
                <label for="db_host">خادم قاعدة البيانات:</label>
                <input type="text" id="db_host" name="db_host" value="localhost" required>
            </div>
            
            <div class="form-group">
                <label for="db_name">اسم قاعدة البيانات:</label>
                <input type="text" id="db_name" name="db_name" value="concours_tunisie" required>
            </div>
            
            <div class="form-group">
                <label for="db_user">اسم المستخدم:</label>
                <input type="text" id="db_user" name="db_user" value="root" required>
            </div>
            
            <div class="form-group">
                <label for="db_pass">كلمة المرور:</label>
                <input type="password" id="db_pass" name="db_pass" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="sample_data" name="sample_data" checked>
                <label for="sample_data">إدراج بيانات تجريبية (موصى به للاختبار)</label>
            </div>
            
            <button type="submit" class="btn" <?php echo !empty($errors) ? 'disabled' : ''; ?>>
                <i class="fas fa-cog"></i>
                بدء الإعداد
            </button>
        </form>
        <?php endif; ?>
        
        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 0.9rem;">
            <p>تأكد من إنشاء قاعدة البيانات أولاً أو امنح صلاحيات إنشاء قواعد البيانات للمستخدم</p>
        </div>
    </div>
</body>
</html>
