<?php
/**
 * XML Sitemap Generator
 * Generates dynamic sitemap for SEO
 */

require_once 'config/database.php';

header('Content-Type: application/xml; charset=utf-8');

$base_url = 'https://tunisieconcours.org';

echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Homepage
echo '<url>' . "\n";
echo '<loc>' . $base_url . '/</loc>' . "\n";
echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
echo '<changefreq>daily</changefreq>' . "\n";
echo '<priority>1.0</priority>' . "\n";
echo '</url>' . "\n";

// Jobs listing page
echo '<url>' . "\n";
echo '<loc>' . $base_url . '/jobs.php</loc>' . "\n";
echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
echo '<changefreq>daily</changefreq>' . "\n";
echo '<priority>0.9</priority>' . "\n";
echo '</url>' . "\n";

// Static pages
$static_pages = [
    '/login.php' => ['changefreq' => 'monthly', 'priority' => '0.5'],
    '/register-type.php' => ['changefreq' => 'monthly', 'priority' => '0.5'],
    '/about.php' => ['changefreq' => 'monthly', 'priority' => '0.6'],
    '/contact.php' => ['changefreq' => 'monthly', 'priority' => '0.6'],
];

foreach ($static_pages as $page => $settings) {
    echo '<url>' . "\n";
    echo '<loc>' . $base_url . $page . '</loc>' . "\n";
    echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
    echo '<changefreq>' . $settings['changefreq'] . '</changefreq>' . "\n";
    echo '<priority>' . $settings['priority'] . '</priority>' . "\n";
    echo '</url>' . "\n";
}

try {
    // Active job postings
    $jobs = $database->fetchAll("
        SELECT slug, updated_at, created_at 
        FROM jobs 
        WHERE is_active = 1 AND slug IS NOT NULL AND slug != ''
        ORDER BY created_at DESC
    ");
    
    foreach ($jobs as $job) {
        echo '<url>' . "\n";
        echo '<loc>' . $base_url . '/emploi/' . htmlspecialchars($job['slug']) . '</loc>' . "\n";
        echo '<lastmod>' . date('Y-m-d', strtotime($job['updated_at'])) . '</lastmod>' . "\n";
        echo '<changefreq>weekly</changefreq>' . "\n";
        echo '<priority>0.8</priority>' . "\n";
        echo '</url>' . "\n";
    }
    
    // Job categories
    $categories = $database->fetchAll("
        SELECT slug, updated_at 
        FROM job_categories 
        WHERE is_active = 1 AND slug IS NOT NULL
    ");
    
    foreach ($categories as $category) {
        echo '<url>' . "\n";
        echo '<loc>' . $base_url . '/jobs.php?category=' . htmlspecialchars($category['slug']) . '</loc>' . "\n";
        echo '<lastmod>' . date('Y-m-d', strtotime($category['updated_at'])) . '</lastmod>' . "\n";
        echo '<changefreq>weekly</changefreq>' . "\n";
        echo '<priority>0.7</priority>' . "\n";
        echo '</url>' . "\n";
    }
    
    // Companies with active jobs
    $companies = $database->fetchAll("
        SELECT DISTINCT c.id, c.updated_at 
        FROM companies c 
        INNER JOIN jobs j ON c.id = j.company_id 
        WHERE c.is_active = 1 AND j.is_active = 1
    ");
    
    foreach ($companies as $company) {
        echo '<url>' . "\n";
        echo '<loc>' . $base_url . '/company/' . $company['id'] . '</loc>' . "\n";
        echo '<lastmod>' . date('Y-m-d', strtotime($company['updated_at'])) . '</lastmod>' . "\n";
        echo '<changefreq>weekly</changefreq>' . "\n";
        echo '<priority>0.6</priority>' . "\n";
        echo '</url>' . "\n";
    }
    
    // Location-based job pages
    $locations = [
        'tunis', 'sfax', 'sousse', 'gabes', 'bizerte', 'ariana', 'gafsa',
        'monastir', 'ben_arous', 'kasserine', 'medenine', 'nabeul', 'tataouine'
    ];
    
    foreach ($locations as $location) {
        echo '<url>' . "\n";
        echo '<loc>' . $base_url . '/jobs.php?location=' . $location . '</loc>' . "\n";
        echo '<lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
        echo '<changefreq>weekly</changefreq>' . "\n";
        echo '<priority>0.6</priority>' . "\n";
        echo '</url>' . "\n";
    }
    
} catch (Exception $e) {
    // Log error but continue generating sitemap
    error_log("Sitemap generation error: " . $e->getMessage());
}

echo '</urlset>' . "\n";
?>
