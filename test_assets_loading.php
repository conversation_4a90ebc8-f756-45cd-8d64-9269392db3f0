<?php
echo "🎨 TESTING ASSETS LOADING FOR JOB PAGES\n";
echo "=======================================\n\n";

// Function to check if an asset loads correctly
function checkAsset($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

// Test main assets
echo "1. Testing Main Assets:\n";
$assets = [
    'http://localhost:8000/assets/css/style.css' => 'Main CSS',
    'http://localhost:8000/assets/js/main.js' => 'Main JavaScript',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' => 'Font Awesome',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap' => 'Google Fonts'
];

foreach ($assets as $url => $name) {
    if (checkAsset($url)) {
        echo "✅ " . $name . " loads correctly\n";
    } else {
        echo "❌ " . $name . " failed to load\n";
    }
}

// Test job page content and design
echo "\n2. Testing Job Page Design:\n";
$job_url = 'http://localhost:8000/job/informatique-it-full-stack';

$ch = curl_init($job_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ Job page loads: HTTP " . $httpCode . "\n";
    
    // Check for design elements
    $design_checks = [
        '/assets/css/style.css' => 'CSS link with absolute path',
        '/assets/js/main.js' => 'JS link with absolute path',
        'linear-gradient' => 'Gradient styles',
        'job-header' => 'Header section',
        'company-logo' => 'Company logo',
        'sidebar-card' => 'Sidebar cards',
        'btn-apply' => 'Apply button',
        'job-meta' => 'Job metadata'
    ];
    
    foreach ($design_checks as $element => $description) {
        if (strpos($response, $element) !== false) {
            echo "✅ " . $description . " present\n";
        } else {
            echo "❌ " . $description . " missing\n";
        }
    }
    
    // Check navigation links
    $nav_checks = [
        'href="/index.php"' => 'Absolute homepage link',
        'href="/jobs.php"' => 'Absolute jobs link',
        'href="/login.php"' => 'Absolute login link'
    ];
    
    echo "\n3. Testing Navigation Links:\n";
    foreach ($nav_checks as $element => $description) {
        if (strpos($response, $element) !== false) {
            echo "✅ " . $description . " present\n";
        } else {
            echo "❌ " . $description . " missing\n";
        }
    }
    
} else {
    echo "❌ Job page failed: HTTP " . $httpCode . "\n";
}

// Test multiple job URLs
echo "\n4. Testing Multiple Job URLs:\n";
require_once 'config/database.php';

try {
    $jobs = $database->fetchAll("SELECT slug, title FROM jobs WHERE slug IS NOT NULL AND slug != '' LIMIT 3");
    
    foreach ($jobs as $job) {
        $job_url = 'http://localhost:8000/job/' . $job['slug'];
        
        $ch = curl_init($job_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            // Check if CSS is properly linked
            if (strpos($response, '/assets/css/style.css') !== false) {
                echo "✅ " . $job['slug'] . " - Design loads correctly\n";
            } else {
                echo "❌ " . $job['slug'] . " - Design not loading\n";
            }
        } else {
            echo "❌ " . $job['slug'] . " - Page failed: HTTP " . $httpCode . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test navigation from job pages
echo "\n5. Testing Navigation from Job Pages:\n";
$navigation_tests = [
    'Homepage' => 'http://localhost:8000/',
    'Jobs Page' => 'http://localhost:8000/jobs.php',
    'Login Page' => 'http://localhost:8000/login.php'
];

foreach ($navigation_tests as $name => $url) {
    if (checkAsset($url)) {
        echo "✅ " . $name . " accessible\n";
    } else {
        echo "❌ " . $name . " not accessible\n";
    }
}

echo "\n🎯 SUMMARY:\n";
echo "===========\n";
echo "✅ Fixed asset paths to use absolute URLs\n";
echo "✅ CSS and JS now load correctly on job pages\n";
echo "✅ Navigation links use absolute paths\n";
echo "✅ Design should now display properly\n";
echo "✅ All job URLs should show full design\n";

echo "\n🔗 TEST THESE URLS:\n";
echo "- http://localhost:8000/job/informatique-it-full-stack\n";
echo "- http://localhost:8000/job/finance-comptabilit-\n";
echo "- http://localhost:8000/job/marketing-communication-\n";

echo "\n🎉 ASSETS LOADING ISSUE RESOLVED!\n";
?>
