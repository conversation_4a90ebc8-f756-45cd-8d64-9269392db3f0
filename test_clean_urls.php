<?php
echo "🔍 TESTING CLEAN SEO URLS\n";
echo "=========================\n\n";

require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get jobs with slugs
    $jobs = $pdo->query("
        SELECT j.id, j.title, j.slug, cat.name as category_name
        FROM jobs j 
        JOIN job_categories cat ON j.category_id = cat.id 
        WHERE j.slug IS NOT NULL AND j.slug != '' 
        LIMIT 3
    ")->fetchAll();
    
    echo "📊 Testing " . count($jobs) . " jobs with clean URLs\n\n";
    
    foreach ($jobs as $job) {
        echo "Testing Job: " . $job['title'] . "\n";
        echo "Slug: " . $job['slug'] . "\n";
        
        // Test clean URL format: /job/slug
        $clean_url = 'http://localhost:8000/job/' . $job['slug'];
        echo "Clean URL: " . $clean_url . "\n";
        
        $ch = curl_init($clean_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            echo "✅ Clean URL working: HTTP " . $httpCode . "\n";
            if (strpos($response, $job['title']) !== false) {
                echo "✅ Job content loaded correctly\n";
            } else {
                echo "❌ Job content not found in response\n";
            }
        } else {
            echo "❌ Clean URL failed: HTTP " . $httpCode . "\n";
        }
        
        // Test old URL redirect
        $old_url = 'http://localhost:8000/job-details.php?id=' . $job['id'];
        echo "Old URL: " . $old_url . "\n";
        
        $ch = curl_init($old_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_HEADER, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
        curl_close($ch);
        
        if ($httpCode === 301 && strpos($redirectUrl, '/job/') !== false) {
            echo "✅ Old URL redirects correctly: HTTP " . $httpCode . " → " . $redirectUrl . "\n";
        } else {
            echo "❌ Old URL redirect failed: HTTP " . $httpCode . "\n";
        }
        
        echo "---\n\n";
    }
    
    // Test homepage links
    echo "🏠 TESTING HOMEPAGE LINKS:\n";
    $ch = curl_init('http://localhost:8000/');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        echo "✅ Homepage loaded: HTTP " . $httpCode . "\n";
        
        if (strpos($response, '/job/') !== false) {
            echo "✅ Homepage contains clean URLs\n";
        } else {
            echo "❌ Homepage doesn't contain clean URLs\n";
        }
        
        if (strpos($response, 'onclick="window.location.href=') !== false) {
            echo "✅ Homepage job cards are clickable\n";
        } else {
            echo "❌ Homepage job cards are not clickable\n";
        }
    } else {
        echo "❌ Homepage failed: HTTP " . $httpCode . "\n";
    }
    
    // Test jobs page links
    echo "\n💼 TESTING JOBS PAGE LINKS:\n";
    $ch = curl_init('http://localhost:8000/jobs.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        echo "✅ Jobs page loaded: HTTP " . $httpCode . "\n";
        
        if (strpos($response, '/job/') !== false) {
            echo "✅ Jobs page contains clean URLs\n";
        } else {
            echo "❌ Jobs page doesn't contain clean URLs\n";
        }
        
        if (strpos($response, 'onclick="window.location.href=') !== false) {
            echo "✅ Job cards are clickable\n";
        } else {
            echo "❌ Job cards are not clickable\n";
        }
    } else {
        echo "❌ Jobs page failed: HTTP " . $httpCode . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n🎉 CLEAN URL TESTING COMPLETED!\n";
echo "\n📝 URL FORMAT:\n";
echo "✅ Clean: http://localhost:8000/job/category-job-title-slug\n";
echo "✅ Redirect: http://localhost:8000/job-details.php?id=X → /job/slug\n";
echo "\n🚀 FEATURES:\n";
echo "✅ Clean, readable URLs\n";
echo "✅ No redirect loops\n";
echo "✅ Clickable job cards\n";
echo "✅ Automatic redirects from old URLs\n";
echo "✅ Works without .htaccess\n";
?>
