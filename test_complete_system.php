<?php
echo "🔍 COMPLETE SYSTEM TEST WITH CURL\n";
echo "==================================\n\n";

// Test 1: Homepage
echo "1. Testing Homepage...\n";
$ch = curl_init('http://localhost:8000/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Homepage: HTTP {$httpCode}\n";
    if (strpos($response, 'Emplois en vedette') !== false) {
        echo "   ✅ French content present\n";
    } else {
        echo "   ❌ French content missing\n";
    }
    if (strpos($response, 'register-type.php') !== false) {
        echo "   ✅ Updated registration link\n";
    } else {
        echo "   ❌ Registration link not updated\n";
    }
} else {
    echo "   ❌ Homepage: HTTP {$httpCode}\n";
}

// Test 2: Registration Type Selector
echo "\n2. Testing Registration Type Selector...\n";
$ch = curl_init('http://localhost:8000/register-type.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Registration type selector: HTTP {$httpCode}\n";
} else {
    echo "   ❌ Registration type selector: HTTP {$httpCode}\n";
}

// Test 3: Candidate Registration
echo "\n3. Testing Candidate Registration...\n";
$ch = curl_init('http://localhost:8000/register-candidate.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'first_name' => 'Test',
    'last_name' => 'User',
    'email' => 'testuser' . time() . '@example.com',
    'password' => 'password123',
    'confirm_password' => 'password123',
    'phone' => '20123456',
    'governorate' => 'tunis'
]));
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Candidate registration: HTTP {$httpCode}\n";
    if (strpos($response, 'créé avec succès') !== false) {
        echo "   ✅ Registration successful\n";
    } else {
        echo "   ❌ Registration failed\n";
    }
} else {
    echo "   ❌ Candidate registration: HTTP {$httpCode}\n";
}

// Test 4: Company Registration
echo "\n4. Testing Company Registration...\n";
$ch = curl_init('http://localhost:8000/register-company.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'company_name' => 'Test Company ' . time(),
    'email' => 'testcompany' . time() . '@example.com',
    'password' => 'password123',
    'confirm_password' => 'password123',
    'phone' => '71123456',
    'website' => 'https://www.test.com',
    'industry' => 'Technology',
    'company_size' => '11-50',
    'governorate' => 'tunis'
]));
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Company registration: HTTP {$httpCode}\n";
    if (strpos($response, 'créé avec succès') !== false) {
        echo "   ✅ Registration successful\n";
    } else {
        echo "   ❌ Registration failed\n";
    }
} else {
    echo "   ❌ Company registration: HTTP {$httpCode}\n";
}

// Test 5: Login as Company
echo "\n5. Testing Company Login...\n";
$ch = curl_init('http://localhost:8000/login.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'email' => '<EMAIL>',
    'password' => 'password123',
    'user_type' => 'company'
]));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
curl_close($ch);

if ($httpCode === 302 && strpos($redirectUrl, 'company-dashboard.php') !== false) {
    echo "   ✅ Company login successful (redirected to dashboard)\n";
} else {
    echo "   ❌ Company login failed: HTTP {$httpCode}\n";
}

// Test 6: Company Dashboard Access
echo "\n6. Testing Company Dashboard...\n";
$ch = curl_init('http://localhost:8000/company-dashboard.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Company dashboard: HTTP {$httpCode}\n";
} else {
    echo "   ❌ Company dashboard: HTTP {$httpCode}\n";
}

// Test 7: Job Posting
echo "\n7. Testing Job Posting...\n";
$ch = curl_init('http://localhost:8000/post-job.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'title' => 'Test Job Position',
    'category_id' => 1,
    'description' => 'This is a test job description',
    'requirements' => 'Test requirements',
    'benefits' => 'Test benefits',
    'salary_min' => 1000,
    'salary_max' => 1500,
    'job_type' => 'cdi',
    'experience_level' => 'junior',
    'education_level' => 'bachelor',
    'location' => 'Tunis',
    'governorate' => 'tunis',
    'remote_work' => 0,
    'application_deadline' => date('Y-m-d', strtotime('+30 days')),
    'is_featured' => 0
]));
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Job posting page: HTTP {$httpCode}\n";
    if (strpos($response, 'publiée avec succès') !== false) {
        echo "   ✅ Job posted successfully\n";
    } else {
        echo "   ❌ Job posting failed\n";
    }
} else {
    echo "   ❌ Job posting page: HTTP {$httpCode}\n";
}

// Test 8: Homepage with Session (should show company navigation)
echo "\n8. Testing Homepage with Company Session...\n";
$ch = curl_init('http://localhost:8000/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Homepage with session: HTTP {$httpCode}\n";
    if (strpos($response, 'Tableau de bord') !== false) {
        echo "   ✅ Company navigation visible\n";
    } else {
        echo "   ❌ Company navigation not visible\n";
    }
    if (strpos($response, 'Déconnexion') !== false) {
        echo "   ✅ Logout option visible\n";
    } else {
        echo "   ❌ Logout option not visible\n";
    }
} else {
    echo "   ❌ Homepage with session: HTTP {$httpCode}\n";
}

// Test 9: Admin Dashboard
echo "\n9. Testing Admin Dashboard...\n";
$ch = curl_init('http://localhost:8000/admin-dashboard.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 302) {
    echo "   ✅ Admin dashboard: HTTP {$httpCode} (redirected to login - correct)\n";
} else {
    echo "   ❌ Admin dashboard: HTTP {$httpCode}\n";
}

// Test 10: Jobs Search
echo "\n10. Testing Jobs Search...\n";
$ch = curl_init('http://localhost:8000/jobs.php');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "   ✅ Jobs search: HTTP {$httpCode}\n";
} else {
    echo "   ❌ Jobs search: HTTP {$httpCode}\n";
}

// Clean up
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

echo "\n🎉 COMPLETE SYSTEM TEST FINISHED!\n";
echo "All major functionality tested with curl.\n";
?>
