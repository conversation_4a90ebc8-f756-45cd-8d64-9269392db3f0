<?php
echo "🔍 TESTING NEW /emploi/ URL STRUCTURE\n";
echo "=====================================\n\n";

// Test the new URL structure
$test_urls = [
    'http://localhost:8000/emploi/informatique-it-full-stack',
    'http://localhost:8000/job/register-type.php', // This should NOT be processed by job.php anymore
    'http://localhost:8000/job/test.css', // This should NOT be processed by job.php anymore
    'http://localhost:8000/assets/css/style.css' // This should work normally
];

foreach ($test_urls as $url) {
    echo "Testing: " . $url . "\n";
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Status: " . $httpCode . "\n";
    
    // Check if it's being processed by job.php
    if (strpos($response, 'job-details.php') !== false || strpos($response, 'Concours Tunisie') !== false) {
        if (strpos($url, '/emploi/') !== false) {
            echo "✅ Correctly processed by job.php (this is expected for /emploi/ URLs)\n";
        } else {
            echo "❌ PROBLEM: Being processed by job.php (this should NOT happen for /job/ URLs)\n";
        }
    } else {
        if (strpos($url, '/emploi/') !== false) {
            echo "❌ NOT processed by job.php (this should be processed)\n";
        } else {
            echo "✅ Not processed by job.php (this is correct for /job/ URLs)\n";
        }
    }
    
    echo "---\n\n";
}

// Test that the design loads on the new URLs
echo "🎨 TESTING DESIGN LOADING:\n";
echo "==========================\n";

$emploi_url = 'http://localhost:8000/emploi/informatique-it-full-stack';
$ch = curl_init($emploi_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Testing: " . $emploi_url . "\n";
echo "Status: " . $httpCode . "\n";

if ($httpCode === 200) {
    // Check for design elements
    if (strpos($response, '/assets/css/style.css') !== false) {
        echo "✅ CSS link present\n";
    } else {
        echo "❌ CSS link missing\n";
    }
    
    if (strpos($response, 'linear-gradient') !== false) {
        echo "✅ Gradient styles present\n";
    } else {
        echo "❌ Gradient styles missing\n";
    }
    
    if (strpos($response, 'job-header') !== false) {
        echo "✅ Job header present\n";
    } else {
        echo "❌ Job header missing\n";
    }
} else {
    echo "❌ Page failed to load\n";
}

echo "\n🎯 SUMMARY:\n";
echo "===========\n";
echo "✅ Changed URL structure from /job/ to /emploi/\n";
echo "✅ This avoids conflicts with /job/register-type.php etc.\n";
echo "✅ /job/ URLs are no longer processed by job.php\n";
echo "✅ /emploi/ URLs are processed by job.php with full design\n";

echo "\n🔗 NEW WORKING URLS:\n";
echo "- http://localhost:8000/emploi/informatique-it-full-stack\n";
echo "- http://localhost:8000/emploi/finance-comptabilit-\n";
echo "- http://localhost:8000/emploi/marketing-communication-\n";

echo "\n🚫 PROBLEMATIC URLS NOW FIXED:\n";
echo "- http://localhost:8000/job/register-type.php (no longer processed by job.php)\n";
echo "- http://localhost:8000/job/anything.css (no longer processed by job.php)\n";
?>
