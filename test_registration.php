<?php
require_once 'config/database.php';

echo "Testing registration system...\n\n";

// Test database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit;
}

// Test user table structure
echo "\nChecking users table structure...\n";
try {
    $result = $pdo->query("DESCRIBE users")->fetchAll();
    echo "✅ Users table exists with " . count($result) . " columns\n";
    
    // Check for required columns
    $columns = array_column($result, 'Field');
    $required = ['first_name', 'last_name', 'email', 'password', 'phone', 'governorate', 'is_active'];
    foreach ($required as $col) {
        if (in_array($col, $columns)) {
            echo "  ✅ Column '{$col}' exists\n";
        } else {
            echo "  ❌ Column '{$col}' missing\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking users table: " . $e->getMessage() . "\n";
}

// Test companies table structure
echo "\nChecking companies table structure...\n";
try {
    $result = $pdo->query("DESCRIBE companies")->fetchAll();
    echo "✅ Companies table exists with " . count($result) . " columns\n";
    
    // Check for required columns
    $columns = array_column($result, 'Field');
    $required = ['name', 'email', 'password', 'phone', 'governorate', 'is_active'];
    foreach ($required as $col) {
        if (in_array($col, $columns)) {
            echo "  ✅ Column '{$col}' exists\n";
        } else {
            echo "  ❌ Column '{$col}' missing\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking companies table: " . $e->getMessage() . "\n";
}

// Test direct insert
echo "\nTesting direct user insert...\n";
try {
    $testData = [
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => 'test_' . time() . '@example.com',
        'password' => password_hash('test123', PASSWORD_DEFAULT),
        'phone' => '20123456',
        'governorate' => 'tunis',
        'is_active' => 0,
        'email_verified' => 0
    ];
    
    $columns = implode(',', array_keys($testData));
    $placeholders = ':' . implode(', :', array_keys($testData));
    $sql = "INSERT INTO users ({$columns}) VALUES ({$placeholders})";
    
    echo "SQL: {$sql}\n";
    echo "Data: " . print_r($testData, true) . "\n";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($testData);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "✅ Direct user insert successful! User ID: {$userId}\n";
        
        // Clean up
        $pdo->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
        echo "✅ Test user cleaned up\n";
    } else {
        echo "❌ Direct user insert failed\n";
    }
} catch (Exception $e) {
    echo "❌ Direct insert error: " . $e->getMessage() . "\n";
}

// Test direct company insert
echo "\nTesting direct company insert...\n";
try {
    $testData = [
        'name' => 'Test Company',
        'email' => 'testcompany_' . time() . '@example.com',
        'password' => password_hash('test123', PASSWORD_DEFAULT),
        'phone' => '71123456',
        'governorate' => 'tunis',
        'is_active' => 0,
        'is_verified' => 0
    ];
    
    $columns = implode(',', array_keys($testData));
    $placeholders = ':' . implode(', :', array_keys($testData));
    $sql = "INSERT INTO companies ({$columns}) VALUES ({$placeholders})";
    
    echo "SQL: {$sql}\n";
    echo "Data: " . print_r($testData, true) . "\n";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($testData);
    
    if ($result) {
        $companyId = $pdo->lastInsertId();
        echo "✅ Direct company insert successful! Company ID: {$companyId}\n";
        
        // Clean up
        $pdo->prepare("DELETE FROM companies WHERE id = ?")->execute([$companyId]);
        echo "✅ Test company cleaned up\n";
    } else {
        echo "❌ Direct company insert failed\n";
    }
} catch (Exception $e) {
    echo "❌ Direct company insert error: " . $e->getMessage() . "\n";
}

// Test database class
echo "\nTesting database class...\n";
try {
    $testData = [
        'first_name' => 'Test',
        'last_name' => 'User2',
        'email' => 'test2_' . time() . '@example.com',
        'password' => password_hash('test123', PASSWORD_DEFAULT),
        'phone' => '20123456',
        'governorate' => 'tunis',
        'is_active' => 0,
        'email_verified' => 0
    ];
    
    $result = $database->insert('users', $testData);
    
    if ($result) {
        echo "✅ Database class insert successful! User ID: {$result}\n";
        
        // Clean up
        $database->query("DELETE FROM users WHERE id = ?", [$result]);
        echo "✅ Test user cleaned up\n";
    } else {
        echo "❌ Database class insert failed\n";
    }
} catch (Exception $e) {
    echo "❌ Database class error: " . $e->getMessage() . "\n";
}

echo "\n🎉 Registration testing completed!\n";
?>
