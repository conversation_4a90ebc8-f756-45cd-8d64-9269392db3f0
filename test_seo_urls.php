<?php
echo "🔍 TESTING SEO URL SYSTEM\n";
echo "=========================\n\n";

require_once 'config/database.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get jobs with slugs
    $jobs = $pdo->query("
        SELECT j.id, j.title, j.slug, cat.name as category_name
        FROM jobs j 
        JOIN job_categories cat ON j.category_id = cat.id 
        WHERE j.slug IS NOT NULL AND j.slug != '' 
        LIMIT 5
    ")->fetchAll();
    
    echo "📊 Found " . count($jobs) . " jobs with slugs\n\n";
    
    foreach ($jobs as $job) {
        echo "Testing Job: " . $job['title'] . "\n";
        echo "Category: " . $job['category_name'] . "\n";
        echo "Slug: " . $job['slug'] . "\n";
        
        // Test new SEO URL
        $seo_url = 'http://localhost:8000/job.php?slug=' . $job['slug'];
        echo "SEO URL: " . $seo_url . "\n";
        
        $ch = curl_init($seo_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            echo "✅ SEO URL working: HTTP " . $httpCode . "\n";
            if (strpos($response, $job['title']) !== false) {
                echo "✅ Job content loaded correctly\n";
            } else {
                echo "❌ Job content not found in response\n";
            }
        } else {
            echo "❌ SEO URL failed: HTTP " . $httpCode . "\n";
        }
        
        // Test old URL redirect
        $old_url = 'http://localhost:8000/job-details.php?id=' . $job['id'];
        echo "Old URL: " . $old_url . "\n";
        
        $ch = curl_init($old_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_HEADER, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
        curl_close($ch);
        
        if ($httpCode === 301 && strpos($redirectUrl, 'job.php?slug=') !== false) {
            echo "✅ Old URL redirects correctly: HTTP " . $httpCode . " → " . $redirectUrl . "\n";
        } else {
            echo "❌ Old URL redirect failed: HTTP " . $httpCode . "\n";
        }
        
        echo "---\n\n";
    }
    
    // Test jobs page links
    echo "🔗 TESTING JOBS PAGE LINKS:\n";
    $ch = curl_init('http://localhost:8000/jobs.php');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        echo "✅ Jobs page loaded: HTTP " . $httpCode . "\n";
        
        // Check for SEO URLs in the response
        if (strpos($response, 'job.php?slug=') !== false) {
            echo "✅ Jobs page contains SEO URLs\n";
        } else {
            echo "❌ Jobs page doesn't contain SEO URLs\n";
        }
        
        // Check for clickable job cards
        if (strpos($response, 'onclick="window.location.href=') !== false) {
            echo "✅ Job cards are clickable\n";
        } else {
            echo "❌ Job cards are not clickable\n";
        }
    } else {
        echo "❌ Jobs page failed: HTTP " . $httpCode . "\n";
    }
    
    // Test homepage links
    echo "\n🏠 TESTING HOMEPAGE LINKS:\n";
    $ch = curl_init('http://localhost:8000/');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        echo "✅ Homepage loaded: HTTP " . $httpCode . "\n";
        
        if (strpos($response, 'job.php?slug=') !== false) {
            echo "✅ Homepage contains SEO URLs\n";
        } else {
            echo "❌ Homepage doesn't contain SEO URLs\n";
        }
        
        if (strpos($response, 'onclick="window.location.href=') !== false) {
            echo "✅ Homepage job cards are clickable\n";
        } else {
            echo "❌ Homepage job cards are not clickable\n";
        }
    } else {
        echo "❌ Homepage failed: HTTP " . $httpCode . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n🎉 SEO URL TESTING COMPLETED!\n";
echo "\n📝 URL FORMAT:\n";
echo "New: http://localhost:8000/job.php?slug=category-job-title\n";
echo "Old: http://localhost:8000/job-details.php?id=X (redirects to new)\n";
echo "\n✅ Features:\n";
echo "- SEO-friendly slugs\n";
echo "- Automatic redirects from old URLs\n";
echo "- Clickable job cards\n";
echo "- Consistent URLs across platform\n";
?>
