<?php
session_start();

echo "🔍 SESSION TEST\n";
echo "===============\n\n";

if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']) {
    echo "✅ User is logged in\n";
    echo "   User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "\n";
    echo "   User Type: " . ($_SESSION['user_type'] ?? 'Not set') . "\n";
    echo "   User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "\n";
    echo "   Email: " . ($_SESSION['user_email'] ?? 'Not set') . "\n";
} else {
    echo "❌ No active session\n";
    echo "   Please login first\n";
}

echo "\n📊 Session Data:\n";
foreach ($_SESSION as $key => $value) {
    echo "   {$key}: {$value}\n";
}

echo "\n🔗 Navigation will show:\n";
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in']) {
    if ($_SESSION['user_type'] === 'company') {
        echo "   - Tableau de bord (Company Dashboard)\n";
        echo "   - Déconnexion (Logout)\n";
    } else {
        echo "   - Mon Profil (User Dashboard)\n";
        echo "   - Déconnexion (Logout)\n";
    }
} else {
    echo "   - Connexion (Login)\n";
    echo "   - S'inscrire (Register)\n";
}
?>
