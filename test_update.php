<?php
/**
 * Test script to debug company profile update
 */

require_once 'config/database.php';

echo "<h2>🔧 Database Update Test</h2>";

try {
    // Test database connection
    echo "<h3>1. Testing Database Connection</h3>";
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connection successful<br>";
    
    // Test companies table structure
    echo "<h3>2. Testing Companies Table</h3>";
    $stmt = $pdo->query("DESCRIBE companies");
    $columns = $stmt->fetchAll();
    echo "✅ Companies table columns:<br>";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})<br>";
    }
    
    // Test if we can fetch a company
    echo "<h3>3. Testing Company Fetch</h3>";
    $stmt = $pdo->prepare("SELECT * FROM companies LIMIT 1");
    $stmt->execute();
    $company = $stmt->fetch();
    if ($company) {
        echo "✅ Found company: " . htmlspecialchars($company['name']) . " (ID: {$company['id']})<br>";
        
        // Test update with minimal data
        echo "<h3>4. Testing Update</h3>";
        $test_data = ['name' => $company['name']]; // Update with same name
        
        $set_clauses = [];
        $values = [];
        foreach ($test_data as $column => $value) {
            $set_clauses[] = "`$column` = ?";
            $values[] = $value;
        }
        
        $sql = "UPDATE `companies` SET " . implode(", ", $set_clauses) . " WHERE id = ?";
        $values[] = $company['id'];
        
        echo "SQL: " . htmlspecialchars($sql) . "<br>";
        echo "Values: " . json_encode($values) . "<br>";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($values);
        $rowCount = $stmt->rowCount();
        
        echo "Execute result: " . ($result ? 'true' : 'false') . "<br>";
        echo "Rows affected: {$rowCount}<br>";
        
        if ($result) {
            echo "✅ Update test successful<br>";
        } else {
            echo "❌ Update test failed<br>";
        }
        
    } else {
        echo "❌ No companies found in database<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
}

echo "<h3>5. Error Log Check</h3>";
$error_log = error_get_last();
if ($error_log) {
    echo "Last error: " . htmlspecialchars($error_log['message']) . "<br>";
} else {
    echo "No errors in error log<br>";
}
?> 