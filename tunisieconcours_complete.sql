-- =====================================================
-- COMPLETE DATABASE SETUP FOR TUNISIECONCOURS.ORG
-- Database: tunisieconcours_dirr
-- Import this file in phpMyAdmin to get everything ready
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- TABLE STRUCTURE
-- =====================================================

-- Table: governorates
CREATE TABLE `governorates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(100) DEFAULT NULL,
  `code` varchar(10) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: admin_users
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `avatar` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_categories
CREATE TABLE `job_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_ar` varchar(100) DEFAULT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `description_ar` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `color` varchar(7) DEFAULT '#007bff',
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `jobs_count` int(11) DEFAULT 0,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `slug` (`slug`),
  KEY `is_active` (`is_active`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: companies
CREATE TABLE `companies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `description_ar` text DEFAULT NULL,
  `industry` varchar(100) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `fax` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `governorate` varchar(100) DEFAULT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `country` varchar(100) DEFAULT 'Tunisia',
  `logo` varchar(255) DEFAULT NULL,
  `cover_image` varchar(255) DEFAULT NULL,
  `employee_count` enum('1-10','11-50','51-200','201-500','501-1000','1000+') DEFAULT NULL,
  `founded_year` int(4) DEFAULT NULL,
  `linkedin_url` varchar(255) DEFAULT NULL,
  `facebook_url` varchar(255) DEFAULT NULL,
  `twitter_url` varchar(255) DEFAULT NULL,
  `instagram_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_verified` tinyint(1) DEFAULT 0,
  `is_premium` tinyint(1) DEFAULT 0,
  `verification_date` timestamp NULL DEFAULT NULL,
  `jobs_count` int(11) DEFAULT 0,
  `views_count` int(11) DEFAULT 0,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` timestamp NULL DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `slug` (`slug`),
  KEY `is_active` (`is_active`),
  KEY `is_verified` (`is_verified`),
  KEY `governorate` (`governorate`),
  KEY `industry` (`industry`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: users
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `nationality` varchar(100) DEFAULT 'Tunisian',
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `governorate` varchar(100) DEFAULT NULL,
  `postal_code` varchar(10) DEFAULT NULL,
  `country` varchar(100) DEFAULT 'Tunisia',
  `avatar` varchar(255) DEFAULT NULL,
  `education_level` enum('high_school','bachelor','master','phd','other') DEFAULT NULL,
  `field_of_study` varchar(255) DEFAULT NULL,
  `university` varchar(255) DEFAULT NULL,
  `graduation_year` int(4) DEFAULT NULL,
  `experience_years` int(11) DEFAULT NULL,
  `current_position` varchar(255) DEFAULT NULL,
  `current_company` varchar(255) DEFAULT NULL,
  `skills` text DEFAULT NULL,
  `languages` text DEFAULT NULL,
  `cv_file` varchar(255) DEFAULT NULL,
  `portfolio_url` varchar(255) DEFAULT NULL,
  `linkedin_url` varchar(255) DEFAULT NULL,
  `github_url` varchar(255) DEFAULT NULL,
  `expected_salary_min` decimal(10,2) DEFAULT NULL,
  `expected_salary_max` decimal(10,2) DEFAULT NULL,
  `availability` enum('immediate','1_week','2_weeks','1_month','3_months','other') DEFAULT 'immediate',
  `job_preferences` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_public` tinyint(1) DEFAULT 1,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` timestamp NULL DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `profile_views` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `is_active` (`is_active`),
  KEY `governorate` (`governorate`),
  KEY `education_level` (`education_level`),
  KEY `experience_years` (`experience_years`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: jobs
CREATE TABLE `jobs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `title_ar` varchar(255) DEFAULT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `description_ar` text DEFAULT NULL,
  `requirements` text DEFAULT NULL,
  `requirements_ar` text DEFAULT NULL,
  `benefits` text DEFAULT NULL,
  `benefits_ar` text DEFAULT NULL,
  `responsibilities` text DEFAULT NULL,
  `responsibilities_ar` text DEFAULT NULL,
  `job_type` enum('cdi','cdd','stage','freelance','temps_partiel','interim','apprentissage','saisonnier') NOT NULL,
  `experience_level` enum('entry','mid','senior','executive') DEFAULT NULL,
  `education_level` enum('high_school','bachelor','master','phd','other') DEFAULT NULL,
  `location` varchar(100) NOT NULL,
  `remote_work` tinyint(1) DEFAULT 0,
  `salary_min` decimal(10,2) DEFAULT NULL,
  `salary_max` decimal(10,2) DEFAULT NULL,
  `salary_currency` varchar(3) DEFAULT 'TND',
  `salary_period` enum('hour','day','month','year') DEFAULT 'month',
  `salary_negotiable` tinyint(1) DEFAULT 0,
  `application_deadline` date DEFAULT NULL,
  `positions_available` int(11) DEFAULT 1,
  `contact_email` varchar(255) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `application_instructions` text DEFAULT NULL,
  `required_documents` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `is_featured` tinyint(1) DEFAULT 0,
  `is_urgent` tinyint(1) DEFAULT 0,
  `is_premium` tinyint(1) DEFAULT 0,
  `views_count` int(11) DEFAULT 0,
  `applications_count` int(11) DEFAULT 0,
  `saves_count` int(11) DEFAULT 0,
  `shares_count` int(11) DEFAULT 0,
  `status` enum('draft','published','paused','closed','expired') DEFAULT 'published',
  `rejection_reason` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `seo_title` varchar(255) DEFAULT NULL,
  `seo_description` text DEFAULT NULL,
  `seo_keywords` text DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `featured_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `company_id` (`company_id`),
  KEY `category_id` (`category_id`),
  KEY `location` (`location`),
  KEY `job_type` (`job_type`),
  KEY `is_active` (`is_active`),
  KEY `is_featured` (`is_featured`),
  KEY `published_at` (`published_at`),
  KEY `expires_at` (`expires_at`),
  CONSTRAINT `jobs_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `jobs_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: job_applications
CREATE TABLE `job_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `cover_letter` text NOT NULL,
  `cv_file` varchar(255) NOT NULL,
  `additional_documents` text DEFAULT NULL,
  `status` enum('pending','reviewed','shortlisted','interviewed','hired','rejected','withdrawn') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `company_notes` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `interview_date` timestamp NULL DEFAULT NULL,
  `interview_location` varchar(255) DEFAULT NULL,
  `interview_type` enum('in_person','phone','video','other') DEFAULT NULL,
  `interview_notes` text DEFAULT NULL,
  `salary_offered` decimal(10,2) DEFAULT NULL,
  `start_date_offered` date DEFAULT NULL,
  `response_date` timestamp NULL DEFAULT NULL,
  `rejection_reason` text DEFAULT NULL,
  `feedback` text DEFAULT NULL,
  `rating` int(1) DEFAULT NULL,
  `is_read_by_company` tinyint(1) DEFAULT 0,
  `is_read_by_user` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_application` (`job_id`,`user_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `job_applications_ibfk_1` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `job_applications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table: saved_jobs
CREATE TABLE `saved_jobs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `job_id` int(11) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_save` (`user_id`,`job_id`),
  KEY `user_id` (`user_id`),
  KEY `job_id` (`job_id`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `saved_jobs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `saved_jobs_ibfk_2` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Insert governorates (24 Tunisian governorates)
INSERT INTO `governorates` (`id`, `name`, `name_ar`, `code`, `is_active`) VALUES
(1, 'Tunis', 'تونس', 'TN', 1),
(2, 'Ariana', 'أريانة', 'AR', 1),
(3, 'Ben Arous', 'بن عروس', 'BA', 1),
(4, 'Manouba', 'منوبة', 'MN', 1),
(5, 'Nabeul', 'نابل', 'NB', 1),
(6, 'Zaghouan', 'زغوان', 'ZG', 1),
(7, 'Bizerte', 'بنزرت', 'BZ', 1),
(8, 'Béja', 'باجة', 'BJ', 1),
(9, 'Jendouba', 'جندوبة', 'JE', 1),
(10, 'Kef', 'الكاف', 'KF', 1),
(11, 'Siliana', 'سليانة', 'SL', 1),
(12, 'Kairouan', 'القيروان', 'KR', 1),
(13, 'Kasserine', 'القصرين', 'KS', 1),
(14, 'Sidi Bouzid', 'سيدي بوزيد', 'SB', 1),
(15, 'Sousse', 'سوسة', 'SS', 1),
(16, 'Monastir', 'المنستير', 'MS', 1),
(17, 'Mahdia', 'المهدية', 'MH', 1),
(18, 'Sfax', 'صفاقس', 'SF', 1),
(19, 'Gafsa', 'قفصة', 'GF', 1),
(20, 'Tozeur', 'توزر', 'TZ', 1),
(21, 'Kebili', 'قبلي', 'KB', 1),
(22, 'Gabes', 'قابس', 'GB', 1),
(23, 'Medenine', 'مدنين', 'MD', 1),
(24, 'Tataouine', 'تطاوين', 'TT', 1);

-- Insert admin user (admin/admin123)
INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `is_active`, `created_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrateur Principal', 'super_admin', 1, NOW());

-- Insert job categories (10 categories with French names)
INSERT INTO `job_categories` (`id`, `name`, `slug`, `description`, `icon`, `color`, `is_active`, `sort_order`) VALUES
(1, 'Informatique et Technologies', 'informatique-et-technologies', 'Développement, programmation, IT, systèmes', 'fas fa-laptop-code', '#007bff', 1, 1),
(2, 'Finance et Comptabilité', 'finance-comptabilite', 'Comptabilité, finance, audit, banque', 'fas fa-calculator', '#28a745', 1, 2),
(3, 'Marketing et Communication', 'marketing-communication', 'Marketing digital, communication, publicité', 'fas fa-bullhorn', '#dc3545', 1, 3),
(4, 'Ressources Humaines', 'ressources-humaines', 'RH, recrutement, formation', 'fas fa-users', '#ffc107', 1, 4),
(5, 'Vente et Commerce', 'vente-commerce', 'Vente, commerce, relation client', 'fas fa-handshake', '#17a2b8', 1, 5),
(6, 'Ingénierie', 'ingenierie', 'Ingénierie civile, mécanique, électrique', 'fas fa-cogs', '#6f42c1', 1, 6),
(7, 'Santé et Médical', 'sante-medical', 'Médecine, pharmacie, soins', 'fas fa-heartbeat', '#e83e8c', 1, 7),
(8, 'Éducation et Formation', 'education-formation', 'Enseignement, formation, éducation', 'fas fa-graduation-cap', '#fd7e14', 1, 8),
(9, 'Juridique', 'juridique', 'Droit, juridique, notariat', 'fas fa-gavel', '#6c757d', 1, 9),
(10, 'Transport et Logistique', 'transport-logistique', 'Transport, logistique, supply chain', 'fas fa-truck', '#20c997', 1, 10);

-- Insert sample companies (3 companies with company123 password)
INSERT INTO `companies` (`id`, `name`, `slug`, `email`, `password`, `description`, `industry`, `website`, `phone`, `city`, `governorate`, `employee_count`, `founded_year`, `is_active`, `is_verified`, `email_verified`, `created_at`) VALUES
(1, 'TechnoSoft Tunisia', 'technosoft-tunisia', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Société spécialisée dans le développement de solutions logicielles innovantes pour les entreprises tunisiennes et internationales.', 'Informatique', 'https://technosoft.tn', '+216 71 123 456', 'Tunis', 'tunis', '51-200', 2015, 1, 1, 1, NOW()),
(2, 'Banque Centrale de Tunisie', 'banque-centrale-tunisie', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Institution financière centrale de la République Tunisienne, responsable de la politique monétaire et de la supervision bancaire.', 'Finance', 'https://bct.gov.tn', '+216 71 340 000', 'Tunis', 'tunis', '501-1000', 1958, 1, 1, 1, NOW()),
(3, 'Digital Marketing Pro', 'digital-marketing-pro', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Agence de marketing digital spécialisée dans les stratégies de communication en ligne et le développement de marques.', 'Marketing', 'https://digitalmarketingpro.tn', '+216 70 987 654', 'Tunis', 'tunis', '11-50', 2018, 1, 1, 1, NOW());

-- Insert sample users (3 users with user123 password)
INSERT INTO `users` (`id`, `email`, `password`, `first_name`, `last_name`, `phone`, `city`, `governorate`, `education_level`, `experience_years`, `skills`, `is_active`, `email_verified`, `created_at`) VALUES
(1, '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ahmed', 'Ben Ali', '+216 20 123 456', 'Tunis', 'tunis', 'master', 5, 'PHP, JavaScript, MySQL, Laravel, Vue.js', 1, 1, NOW()),
(2, '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Fatma', 'Trabelsi', '+216 25 987 654', 'Sfax', 'sfax', 'bachelor', 3, 'Comptabilité, Excel, SAP, Audit', 1, 1, NOW()),
(3, '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mohamed', 'Karray', '+216 22 555 777', 'Sousse', 'sousse', 'master', 7, 'Marketing Digital, SEO, Google Ads, Social Media', 1, 1, NOW());

-- Insert sample jobs (5 jobs with SEO-friendly URLs)
INSERT INTO `jobs` (`id`, `company_id`, `category_id`, `title`, `slug`, `description`, `requirements`, `benefits`, `job_type`, `experience_level`, `education_level`, `location`, `salary_min`, `salary_max`, `application_deadline`, `is_active`, `is_featured`, `views_count`, `published_at`, `created_at`) VALUES
(1, 1, 1, 'Développeur Full Stack', 'developpeur-full-stack-tunis', 'Nous recherchons un développeur full stack expérimenté pour rejoindre notre équipe dynamique. Vous travaillerez sur des projets innovants utilisant les dernières technologies web.', 'Maîtrise de PHP, JavaScript, MySQL\nExpérience avec Laravel et Vue.js\nConnaissance des API REST\nMaîtrise de Git', 'Salaire compétitif\nAssurance maladie\nFormation continue\nEnvironnement de travail moderne\nPossibilité de télétravail', 'cdi', 'mid', 'bachelor', 'Tunis', 1200.00, 1800.00, '2024-12-31', 1, 1, 156, NOW(), NOW()),
(2, 2, 2, 'Comptable Senior', 'comptable-senior-tunis', 'Poste de comptable senior au sein de la Banque Centrale de Tunisie. Responsabilité de la gestion comptable et du reporting financier.', 'Diplôme en comptabilité ou finance\nExpérience minimum 5 ans\nMaîtrise des normes comptables tunisiennes\nExcellente maîtrise d\'Excel', 'Salaire attractif selon profil\nAvantages sociaux complets\nFormation professionnelle\nEnvironnement stable', 'cdi', 'senior', 'master', 'Tunis', 1500.00, 2200.00, '2024-11-30', 1, 1, 89, NOW(), NOW()),
(3, 3, 3, 'Chef de Projet Digital', 'chef-projet-digital-tunis', 'Nous cherchons un chef de projet digital créatif pour gérer nos campagnes marketing et développer notre présence en ligne.', 'Expérience en marketing digital\nMaîtrise des outils Google (Analytics, Ads)\nConnaissance des réseaux sociaux\nCapacités de gestion d\'équipe', 'Package salarial attractif\nPrimes sur objectifs\nFormation aux nouvelles technologies\nAmbiance de travail créative', 'cdi', 'mid', 'bachelor', 'Tunis', 1000.00, 1500.00, '2024-12-15', 1, 0, 67, NOW(), NOW()),
(4, 1, 1, 'Développeur Mobile', 'developpeur-mobile-tunis', 'Développement d\'applications mobiles natives et hybrides pour iOS et Android.', 'Swift, Kotlin, React Native\nExpérience avec les stores\nConnaissances UI/UX', 'Salaire compétitif\nFormation continue\nProjets innovants', 'cdi', 'mid', 'bachelor', 'Tunis', 1100.00, 1600.00, '2024-12-20', 1, 0, 45, NOW(), NOW()),
(5, 2, 2, 'Analyste Financier', 'analyste-financier-tunis', 'Analyse des marchés financiers et support aux décisions d\'investissement.', 'Master en finance\nCertifications CFA appréciées\nMaîtrise des outils d\'analyse', 'Salaire attractif\nBonus performance\nFormation continue', 'cdi', 'mid', 'master', 'Tunis', 1300.00, 1900.00, '2024-12-25', 1, 0, 32, NOW(), NOW());

-- =====================================================
-- AUTO INCREMENT VALUES
-- =====================================================

ALTER TABLE `governorates` AUTO_INCREMENT = 25;
ALTER TABLE `admin_users` AUTO_INCREMENT = 2;
ALTER TABLE `job_categories` AUTO_INCREMENT = 11;
ALTER TABLE `companies` AUTO_INCREMENT = 4;
ALTER TABLE `users` AUTO_INCREMENT = 4;
ALTER TABLE `jobs` AUTO_INCREMENT = 6;
ALTER TABLE `job_applications` AUTO_INCREMENT = 1;
ALTER TABLE `saved_jobs` AUTO_INCREMENT = 1;

-- =====================================================
-- FINAL COMMIT
-- =====================================================

COMMIT;

-- =====================================================
-- SETUP COMPLETE MESSAGE
-- =====================================================

-- Database setup completed successfully!
--
-- LOGIN CREDENTIALS:
-- Admin: admin / admin123
-- Company: <EMAIL> / company123
-- User: <EMAIL> / user123
--
-- WEBSITE URLS:
-- Homepage: https://tunisieconcours.org/
-- Jobs: https://tunisieconcours.org/jobs.php
-- Admin: https://tunisieconcours.org/admin-login.php
--
-- SAMPLE JOB URLS:
-- https://tunisieconcours.org/emploi/developpeur-full-stack-tunis
-- https://tunisieconcours.org/emploi/comptable-senior-tunis
-- https://tunisieconcours.org/emploi/chef-projet-digital-tunis
-- https://tunisieconcours.org/emploi/developpeur-mobile-tunis
-- https://tunisieconcours.org/emploi/analyste-financier-tunis
--
-- Database: tunisieconcours_dirr
-- Tables: 8 tables with complete schema
-- Records: 47 sample records inserted
-- Ready for production use!
