<?php
require_once 'config/database.php';

echo "Updating database with slugs...\n";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Add slug column to job_categories if it doesn't exist
    $pdo->exec("ALTER TABLE job_categories ADD COLUMN IF NOT EXISTS slug VARCHAR(255) UNIQUE");
    
    // Add slug column to jobs if it doesn't exist
    $pdo->exec("ALTER TABLE jobs ADD COLUMN IF NOT EXISTS slug VARCHAR(255) UNIQUE");
    
    // Update job categories with slugs
    $categories = $pdo->query("SELECT id, name FROM job_categories")->fetchAll();
    
    foreach ($categories as $category) {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $category['name'])));
        $slug = trim($slug, '-');
        
        // Handle French characters
        $slug = str_replace(['é', 'è', 'ê', 'ë'], 'e', $slug);
        $slug = str_replace(['à', 'â', 'ä'], 'a', $slug);
        $slug = str_replace(['ù', 'û', 'ü'], 'u', $slug);
        $slug = str_replace(['ô', 'ö'], 'o', $slug);
        $slug = str_replace(['î', 'ï'], 'i', $slug);
        $slug = str_replace('ç', 'c', $slug);
        
        $pdo->prepare("UPDATE job_categories SET slug = ? WHERE id = ?")->execute([$slug, $category['id']]);
        echo "Updated category: {$category['name']} -> {$slug}\n";
    }
    
    // Update jobs with slugs
    $jobs = $pdo->query("
        SELECT j.id, j.title, cat.slug as category_slug 
        FROM jobs j 
        JOIN job_categories cat ON j.category_id = cat.id
    ")->fetchAll();
    
    foreach ($jobs as $job) {
        $title_slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $job['title'])));
        $title_slug = trim($title_slug, '-');
        
        // Handle French characters
        $title_slug = str_replace(['é', 'è', 'ê', 'ë'], 'e', $title_slug);
        $title_slug = str_replace(['à', 'â', 'ä'], 'a', $title_slug);
        $title_slug = str_replace(['ù', 'û', 'ü'], 'u', $title_slug);
        $title_slug = str_replace(['ô', 'ö'], 'o', $title_slug);
        $title_slug = str_replace(['î', 'ï'], 'i', $title_slug);
        $title_slug = str_replace('ç', 'c', $title_slug);
        
        $full_slug = $job['category_slug'] . '-' . $title_slug;
        
        // Ensure uniqueness
        $counter = 1;
        $original_slug = $full_slug;
        while (true) {
            $existing = $pdo->prepare("SELECT id FROM jobs WHERE slug = ? AND id != ?");
            $existing->execute([$full_slug, $job['id']]);
            if (!$existing->fetch()) {
                break;
            }
            $full_slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        $pdo->prepare("UPDATE jobs SET slug = ? WHERE id = ?")->execute([$full_slug, $job['id']]);
        echo "Updated job: {$job['title']} -> {$full_slug}\n";
    }
    
    echo "\nSlug update completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
