<?php
require_once 'config/database.php';

// Check if user is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['user_type'] !== 'candidate') {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$user = $database->fetch("SELECT * FROM users WHERE id = ?", [$user_id]);

if (!$user) {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Get user's job applications
$applications = $database->fetchAll("
    SELECT ja.*, j.title, j.salary_min, j.salary_max, j.salary_currency, 
           c.name as company_name, c.logo as company_logo
    FROM job_applications ja
    JOIN jobs j ON ja.job_id = j.id
    JOIN companies c ON j.company_id = c.id
    WHERE ja.user_id = ?
    ORDER BY ja.applied_at DESC
    LIMIT 10
", [$user_id]);

// Get recommended jobs
$recommended_jobs = $database->fetchAll("
    SELECT j.*, c.name as company_name, c.logo as company_logo,
           cat.name as category_name
    FROM jobs j
    JOIN companies c ON j.company_id = c.id
    JOIN job_categories cat ON j.category_id = cat.id
    WHERE j.is_active = 1 AND c.is_active = 1
    AND j.governorate = ?
    ORDER BY j.created_at DESC
    LIMIT 6
", [$user['governorate']]);
?>

<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord - Concours Tunisie</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .dashboard-container {
            padding-top: 100px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .dashboard-header {
            background: white;
            padding: 30px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .welcome-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info h1 {
            color: #2c5aa0;
            margin-bottom: 5px;
        }
        
        .user-info p {
            color: #666;
        }
        
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            color: #2c5aa0;
            margin-bottom: 15px;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-card p {
            color: #666;
        }
        
        .dashboard-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .section-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-header h2 {
            color: #333;
            margin: 0;
        }
        
        .section-content {
            padding: 25px;
        }
        
        .application-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .company-logo {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin-right: 15px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .application-info {
            flex: 1;
        }
        
        .application-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        
        .application-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-reviewed {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-shortlisted {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .job-card-mini {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }
        
        .job-card-mini:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .job-card-mini h4 {
            margin: 0 0 5px 0;
            color: #2c5aa0;
        }
        
        .job-card-mini p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .welcome-section {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-briefcase"></i>
                        <span>Concours Tunisie</span>
                    </a>
                </div>
                
                <div class="nav-menu">
                    <ul class="nav-links">
                        <li><a href="index.php" class="nav-link">Accueil</a></li>
                        <li><a href="jobs.php" class="nav-link">Emplois</a></li>
                        <li><a href="user-dashboard.php" class="nav-link active">Tableau de bord</a></li>
                    </ul>
                    
                    <div class="nav-actions">
                        <span style="margin-right: 15px;">Bonjour, <?php echo htmlspecialchars($user['first_name']); ?></span>
                        <a href="logout.php" class="btn btn-outline">Déconnexion</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="container">
                <div class="welcome-section">
                    <div class="user-info">
                        <h1>Bienvenue, <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>!</h1>
                        <p>Gérez vos candidatures et trouvez votre emploi idéal</p>
                    </div>
                    <div class="user-actions">
                        <a href="profile.php" class="btn btn-primary">
                            <i class="fas fa-user-edit"></i>
                            Modifier le profil
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="dashboard-stats">
                <div class="stat-card">
                    <i class="fas fa-paper-plane"></i>
                    <h3><?php echo count($applications); ?></h3>
                    <p>Candidatures envoyées</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-eye"></i>
                    <h3><?php echo count(array_filter($applications, fn($app) => $app['status'] === 'reviewed')); ?></h3>
                    <p>Candidatures vues</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-star"></i>
                    <h3><?php echo count(array_filter($applications, fn($app) => $app['status'] === 'shortlisted')); ?></h3>
                    <p>Présélections</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-briefcase"></i>
                    <h3><?php echo count($recommended_jobs); ?></h3>
                    <p>Emplois recommandés</p>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="main-content">
                    <div class="section-card">
                        <div class="section-header">
                            <h2>Mes candidatures récentes</h2>
                            <a href="my-applications.php" class="btn btn-outline">Voir tout</a>
                        </div>
                        <div class="section-content">
                            <?php if (empty($applications)): ?>
                                <div style="text-align: center; padding: 40px; color: #666;">
                                    <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 20px;"></i>
                                    <p>Vous n'avez pas encore postulé à des emplois.</p>
                                    <a href="jobs.php" class="btn btn-primary">Parcourir les emplois</a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($applications as $app): ?>
                                    <div class="application-item">
                                        <div class="company-logo">
                                            <?php if ($app['company_logo']): ?>
                                                <img src="uploads/logos/<?php echo $app['company_logo']; ?>" alt="Logo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                                            <?php else: ?>
                                                <i class="fas fa-building" style="color: #ccc;"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="application-info">
                                            <h4><?php echo htmlspecialchars($app['title']); ?></h4>
                                            <p><?php echo htmlspecialchars($app['company_name']); ?></p>
                                            <p>Postulé le <?php echo date('d/m/Y', strtotime($app['applied_at'])); ?></p>
                                        </div>
                                        <div class="status-badge status-<?php echo $app['status']; ?>">
                                            <?php
                                            $statuses = [
                                                'pending' => 'En attente',
                                                'reviewed' => 'Examinée',
                                                'shortlisted' => 'Présélectionné',
                                                'rejected' => 'Rejetée',
                                                'hired' => 'Embauché'
                                            ];
                                            echo $statuses[$app['status']] ?? $app['status'];
                                            ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="sidebar">
                    <div class="section-card">
                        <div class="section-header">
                            <h2>Emplois recommandés</h2>
                        </div>
                        <div class="section-content">
                            <?php if (empty($recommended_jobs)): ?>
                                <p style="color: #666; text-align: center;">Aucun emploi recommandé pour le moment.</p>
                            <?php else: ?>
                                <?php foreach ($recommended_jobs as $job): ?>
                                    <div class="job-card-mini">
                                        <h4><a href="job-details.php?id=<?php echo $job['id']; ?>" style="text-decoration: none; color: #2c5aa0;"><?php echo htmlspecialchars($job['title']); ?></a></h4>
                                        <p><?php echo htmlspecialchars($job['company_name']); ?></p>
                                        <p><?php echo htmlspecialchars($job['category_name']); ?></p>
                                        <?php if ($job['salary_min'] && $job['salary_max']): ?>
                                            <p style="color: #28a745; font-weight: 600;">
                                                <?php echo number_format($job['salary_min']) . ' - ' . number_format($job['salary_max']) . ' ' . $job['salary_currency']; ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
