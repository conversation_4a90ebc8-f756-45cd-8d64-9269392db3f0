<?php
echo "🎨 VERIFYING DESIGN FIX FOR JOB PAGES\n";
echo "====================================\n\n";

// Test job page response
$job_url = 'http://localhost:8000/job/informatique-it-full-stack';

$ch = curl_init($job_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Testing: " . $job_url . "\n";
echo "HTTP Status: " . $httpCode . "\n\n";

if ($httpCode === 200) {
    echo "✅ Page loads successfully\n\n";
    
    // Check for absolute paths
    echo "Checking for absolute asset paths:\n";
    
    if (strpos($response, 'href="/assets/css/style.css"') !== false) {
        echo "✅ CSS uses absolute path: /assets/css/style.css\n";
    } else {
        echo "❌ CSS absolute path not found\n";
    }
    
    if (strpos($response, 'src="/assets/js/main.js"') !== false) {
        echo "✅ JS uses absolute path: /assets/js/main.js\n";
    } else {
        echo "❌ JS absolute path not found\n";
    }
    
    if (strpos($response, 'src="/uploads/logos/') !== false) {
        echo "✅ Images use absolute path: /uploads/logos/\n";
    } else {
        echo "✅ No company logos to check (this is normal)\n";
    }
    
    // Check for navigation links
    echo "\nChecking navigation links:\n";
    
    if (strpos($response, 'href="/index.php"') !== false) {
        echo "✅ Homepage link uses absolute path\n";
    } else {
        echo "❌ Homepage link not absolute\n";
    }
    
    if (strpos($response, 'href="/jobs.php"') !== false) {
        echo "✅ Jobs link uses absolute path\n";
    } else {
        echo "❌ Jobs link not absolute\n";
    }
    
    // Check for design elements
    echo "\nChecking design elements:\n";
    
    if (strpos($response, '<style>') !== false) {
        echo "✅ Inline styles present\n";
    } else {
        echo "❌ No inline styles found\n";
    }
    
    if (strpos($response, 'job-header') !== false) {
        echo "✅ Job header section present\n";
    } else {
        echo "❌ Job header missing\n";
    }
    
    if (strpos($response, 'gradient') !== false) {
        echo "✅ Gradient styles present\n";
    } else {
        echo "❌ Gradient styles missing\n";
    }
    
} else {
    echo "❌ Page failed to load: HTTP " . $httpCode . "\n";
}

// Test CSS file directly
echo "\n" . str_repeat("=", 50) . "\n";
echo "Testing CSS file directly:\n";

$css_url = 'http://localhost:8000/assets/css/style.css';
$ch = curl_init($css_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ CSS file accessible: " . $css_url . "\n";
} else {
    echo "❌ CSS file not accessible: HTTP " . $httpCode . "\n";
}

// Test JS file directly
$js_url = 'http://localhost:8000/assets/js/main.js';
$ch = curl_init($js_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_NOBODY, true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ JS file accessible: " . $js_url . "\n";
} else {
    echo "❌ JS file not accessible: HTTP " . $httpCode . "\n";
}

echo "\n🎯 SUMMARY:\n";
echo "===========\n";
echo "The issue was that job pages accessed via /job/slug URLs\n";
echo "were using relative paths for assets, which broke because\n";
echo "the browser thought it was in a /job/ directory.\n\n";

echo "✅ FIXES APPLIED:\n";
echo "- Changed 'assets/css/style.css' to '/assets/css/style.css'\n";
echo "- Changed 'assets/js/main.js' to '/assets/js/main.js'\n";
echo "- Changed 'uploads/logos/' to '/uploads/logos/'\n";
echo "- Changed all navigation links to absolute paths\n";
echo "- Changed all footer links to absolute paths\n\n";

echo "🎉 DESIGN SHOULD NOW LOAD CORRECTLY!\n";
echo "\n🔗 TEST THESE URLS:\n";
echo "- http://localhost:8000/job/informatique-it-full-stack\n";
echo "- http://localhost:8000/job/finance-comptabilit-\n";
echo "- http://localhost:8000/job/marketing-communication-\n";
?>
